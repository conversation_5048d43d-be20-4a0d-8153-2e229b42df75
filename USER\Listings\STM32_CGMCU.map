Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    system_stm32f10x.o(i.SetSysClock) refers to system_stm32f10x.o(i.SetSysClockTo72) for SetSysClockTo72
    system_stm32f10x.o(i.SystemCoreClockUpdate) refers to system_stm32f10x.o(.data) for SystemCoreClock
    system_stm32f10x.o(i.SystemInit) refers to system_stm32f10x.o(i.SetSysClock) for SetSysClock
    main.o(i.main) refers to system_stm32f10x.o(i.SystemInit) for SystemInit
    main.o(i.main) refers to delay.o(i.delay_init) for delay_init
    main.o(i.main) refers to led.o(i.LED_Init) for LED_Init
    main.o(i.main) refers to led.o(i.LED_On) for LED_On
    main.o(i.main) refers to usart.o(i.USART1_Config) for USART1_Config
    main.o(i.main) refers to mfrc522.o(i.MFRC522_Init) for MFRC522_Init
    main.o(i.main) refers to printf8.o(i.__0printf$8) for __2printf
    main.o(i.main) refers to card_manager.o(i.CARD_InitDatabase) for CARD_InitDatabase
    main.o(i.main) refers to delay.o(i.delay_ms) for delay_ms
    main.o(i.main) refers to card_manager.o(i.CARD_Proc) for CARD_Proc
    led.o(i.LED_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    led.o(i.LED_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    led.o(i.LED_Init) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    led.o(i.LED_Off) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    led.o(i.LED_On) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    led.o(i.LED_Toggle) refers to stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit) for GPIO_ReadOutputDataBit
    led.o(i.LED_Toggle) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    led.o(i.LED_Twinkle) refers to led.o(i.LED_On) for LED_On
    led.o(i.LED_Twinkle) refers to delay.o(i.delay_ms) for delay_ms
    led.o(i.LED_Twinkle) refers to led.o(i.LED_Off) for LED_Off
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_Refresh) for OLED_Refresh
    oled.o(i.OLED_Clear) refers to oled.o(.bss) for OLED_GRAM
    oled.o(i.OLED_ColorTurn) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_DisPlay_Off) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_DisPlay_On) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_DisplayTurn) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_DrawCircle) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawLine) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawPoint) refers to oled.o(.bss) for OLED_GRAM
    oled.o(i.OLED_I2C_Start) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    oled.o(i.OLED_I2C_Start) refers to oled.o(i.IIC_delay) for IIC_delay
    oled.o(i.OLED_I2C_Start) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    oled.o(i.OLED_I2C_Stop) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    oled.o(i.OLED_I2C_Stop) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    oled.o(i.OLED_I2C_Stop) refers to oled.o(i.IIC_delay) for IIC_delay
    oled.o(i.OLED_I2C_WaitAck) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    oled.o(i.OLED_I2C_WaitAck) refers to oled.o(i.IIC_delay) for IIC_delay
    oled.o(i.OLED_I2C_WaitAck) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    oled.o(i.OLED_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    oled.o(i.OLED_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    oled.o(i.OLED_Init) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    oled.o(i.OLED_Init) refers to delay.o(i.delay_ms) for delay_ms
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_Refresh) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Refresh) refers to oled.o(i.OLED_I2C_Start) for OLED_I2C_Start
    oled.o(i.OLED_Refresh) refers to oled.o(i.OLED_Send_Byte) for OLED_Send_Byte
    oled.o(i.OLED_Refresh) refers to oled.o(i.OLED_I2C_WaitAck) for OLED_I2C_WaitAck
    oled.o(i.OLED_Refresh) refers to oled.o(i.OLED_I2C_Stop) for OLED_I2C_Stop
    oled.o(i.OLED_Refresh) refers to oled.o(.bss) for OLED_GRAM
    oled.o(i.OLED_ScrollDisplay) refers to oled.o(i.OLED_ShowChinese) for OLED_ShowChinese
    oled.o(i.OLED_ScrollDisplay) refers to oled.o(i.OLED_Refresh) for OLED_Refresh
    oled.o(i.OLED_ScrollDisplay) refers to oled.o(.bss) for OLED_GRAM
    oled.o(i.OLED_Send_Byte) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    oled.o(i.OLED_Send_Byte) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    oled.o(i.OLED_Send_Byte) refers to oled.o(i.IIC_delay) for IIC_delay
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_Refresh) for OLED_Refresh
    oled.o(i.OLED_ShowChar) refers to oled.o(.constdata) for asc2_0806
    oled.o(i.OLED_ShowChinese) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_ShowChinese) refers to oled.o(i.OLED_Refresh) for OLED_Refresh
    oled.o(i.OLED_ShowChinese) refers to oled.o(.constdata) for Hzk1
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_Refresh) for OLED_Refresh
    oled.o(i.OLED_ShowPicture) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_ShowPicture) refers to oled.o(i.OLED_Refresh) for OLED_Refresh
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_Refresh) for OLED_Refresh
    oled.o(i.OLED_WR_Byte) refers to oled.o(i.OLED_I2C_Start) for OLED_I2C_Start
    oled.o(i.OLED_WR_Byte) refers to oled.o(i.OLED_Send_Byte) for OLED_Send_Byte
    oled.o(i.OLED_WR_Byte) refers to oled.o(i.OLED_I2C_WaitAck) for OLED_I2C_WaitAck
    oled.o(i.OLED_WR_Byte) refers to oled.o(i.OLED_I2C_Stop) for OLED_I2C_Stop
    mfrc522.o(i.CalulateCRC) refers to mfrc522.o(i.ClearBitMask) for ClearBitMask
    mfrc522.o(i.CalulateCRC) refers to mfrc522.o(i.Write_MFRC522) for Write_MFRC522
    mfrc522.o(i.CalulateCRC) refers to mfrc522.o(i.SetBitMask) for SetBitMask
    mfrc522.o(i.CalulateCRC) refers to mfrc522.o(i.Read_MFRC522) for Read_MFRC522
    mfrc522.o(i.ClearBitMask) refers to mfrc522.o(i.Read_MFRC522) for Read_MFRC522
    mfrc522.o(i.ClearBitMask) refers to mfrc522.o(i.Write_MFRC522) for Write_MFRC522
    mfrc522.o(i.MFRC522_AntennaOff) refers to mfrc522.o(i.ClearBitMask) for ClearBitMask
    mfrc522.o(i.MFRC522_AntennaOn) refers to mfrc522.o(i.Read_MFRC522) for Read_MFRC522
    mfrc522.o(i.MFRC522_AntennaOn) refers to printf8.o(i.__0printf$8) for __2printf
    mfrc522.o(i.MFRC522_AntennaOn) refers to mfrc522.o(i.SetBitMask) for SetBitMask
    mfrc522.o(i.MFRC522_AntennaOn) refers to mfrc522.o(i.Write_MFRC522) for Write_MFRC522
    mfrc522.o(i.MFRC522_Anticoll) refers to mfrc522.o(i.ClearBitMask) for ClearBitMask
    mfrc522.o(i.MFRC522_Anticoll) refers to mfrc522.o(i.Write_MFRC522) for Write_MFRC522
    mfrc522.o(i.MFRC522_Anticoll) refers to mfrc522.o(i.MFRC522_ToCard) for MFRC522_ToCard
    mfrc522.o(i.MFRC522_Anticoll) refers to mfrc522.o(i.SetBitMask) for SetBitMask
    mfrc522.o(i.MFRC522_AuthState) refers to mfrc522.o(i.MFRC522_ToCard) for MFRC522_ToCard
    mfrc522.o(i.MFRC522_AuthState) refers to mfrc522.o(i.Read_MFRC522) for Read_MFRC522
    mfrc522.o(i.MFRC522_Halt) refers to mfrc522.o(i.CalulateCRC) for CalulateCRC
    mfrc522.o(i.MFRC522_Halt) refers to mfrc522.o(i.MFRC522_ToCard) for MFRC522_ToCard
    mfrc522.o(i.MFRC522_Init) refers to printf8.o(i.__0printf$8) for __2printf
    mfrc522.o(i.MFRC522_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    mfrc522.o(i.MFRC522_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    mfrc522.o(i.MFRC522_Init) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    mfrc522.o(i.MFRC522_Init) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    mfrc522.o(i.MFRC522_Init) refers to delay.o(i.delay_us) for delay_us
    mfrc522.o(i.MFRC522_Init) refers to mfrc522.o(i.MFRC522_Reset) for MFRC522_Reset
    mfrc522.o(i.MFRC522_Init) refers to mfrc522.o(i.Write_MFRC522) for Write_MFRC522
    mfrc522.o(i.MFRC522_Init) refers to mfrc522.o(i.MFRC522_AntennaOn) for MFRC522_AntennaOn
    mfrc522.o(i.MFRC522_Init) refers to mfrc522.o(i.Read_MFRC522) for Read_MFRC522
    mfrc522.o(i.MFRC522_Init) refers to mfrc522.o(i.MFRC522_Request) for MFRC522_Request
    mfrc522.o(i.MFRC522_Init) refers to mfrc522.o(.conststring) for .conststring
    mfrc522.o(i.MFRC522_Read) refers to mfrc522.o(i.CalulateCRC) for CalulateCRC
    mfrc522.o(i.MFRC522_Read) refers to mfrc522.o(i.MFRC522_ToCard) for MFRC522_ToCard
    mfrc522.o(i.MFRC522_Request) refers to printf8.o(i.__0printf$8) for __2printf
    mfrc522.o(i.MFRC522_Request) refers to mfrc522.o(i.ClearBitMask) for ClearBitMask
    mfrc522.o(i.MFRC522_Request) refers to mfrc522.o(i.Write_MFRC522) for Write_MFRC522
    mfrc522.o(i.MFRC522_Request) refers to mfrc522.o(i.SetBitMask) for SetBitMask
    mfrc522.o(i.MFRC522_Request) refers to mfrc522.o(i.MFRC522_ToCard) for MFRC522_ToCard
    mfrc522.o(i.MFRC522_Request) refers to mfrc522.o(.data) for debug_count
    mfrc522.o(i.MFRC522_Reset) refers to mfrc522.o(i.Write_MFRC522) for Write_MFRC522
    mfrc522.o(i.MFRC522_Reset) refers to delay.o(i.delay_us) for delay_us
    mfrc522.o(i.MFRC522_SelectTag) refers to mfrc522.o(i.CalulateCRC) for CalulateCRC
    mfrc522.o(i.MFRC522_SelectTag) refers to mfrc522.o(i.ClearBitMask) for ClearBitMask
    mfrc522.o(i.MFRC522_SelectTag) refers to mfrc522.o(i.MFRC522_ToCard) for MFRC522_ToCard
    mfrc522.o(i.MFRC522_ToCard) refers to mfrc522.o(i.Write_MFRC522) for Write_MFRC522
    mfrc522.o(i.MFRC522_ToCard) refers to mfrc522.o(i.ClearBitMask) for ClearBitMask
    mfrc522.o(i.MFRC522_ToCard) refers to mfrc522.o(i.SetBitMask) for SetBitMask
    mfrc522.o(i.MFRC522_ToCard) refers to mfrc522.o(i.Read_MFRC522) for Read_MFRC522
    mfrc522.o(i.MFRC522_ToCard) refers to printf8.o(i.__0printf$8) for __2printf
    mfrc522.o(i.MFRC522_ToCard) refers to mfrc522.o(.data) for tocard_debug_count
    mfrc522.o(i.MFRC522_Write) refers to mfrc522.o(i.CalulateCRC) for CalulateCRC
    mfrc522.o(i.MFRC522_Write) refers to mfrc522.o(i.MFRC522_ToCard) for MFRC522_ToCard
    mfrc522.o(i.Read_MFRC522) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    mfrc522.o(i.Read_MFRC522) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    mfrc522.o(i.Read_MFRC522) refers to delay.o(i.delay_us) for delay_us
    mfrc522.o(i.Read_MFRC522) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    mfrc522.o(i.SetBitMask) refers to mfrc522.o(i.Read_MFRC522) for Read_MFRC522
    mfrc522.o(i.SetBitMask) refers to mfrc522.o(i.Write_MFRC522) for Write_MFRC522
    mfrc522.o(i.Write_MFRC522) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    mfrc522.o(i.Write_MFRC522) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    mfrc522.o(i.Write_MFRC522) refers to delay.o(i.delay_us) for delay_us
    card_manager.o(i.CARD_InitDatabase) refers to memseta.o(.text) for __aeabi_memclr
    card_manager.o(i.CARD_InitDatabase) refers to strcpy.o(.text) for strcpy
    card_manager.o(i.CARD_InitDatabase) refers to printf8.o(i.__0printf$8) for __2printf
    card_manager.o(i.CARD_InitDatabase) refers to card_manager.o(.bss) for card_arr
    card_manager.o(i.CARD_Proc) refers to memseta.o(.text) for __aeabi_memclr4
    card_manager.o(i.CARD_Proc) refers to mfrc522.o(i.MFRC522_Request) for MFRC522_Request
    card_manager.o(i.CARD_Proc) refers to printf8.o(i.__0printf$8) for __2printf
    card_manager.o(i.CARD_Proc) refers to mfrc522.o(i.MFRC522_Reset) for MFRC522_Reset
    card_manager.o(i.CARD_Proc) refers to mfrc522.o(i.MFRC522_AntennaOff) for MFRC522_AntennaOff
    card_manager.o(i.CARD_Proc) refers to mfrc522.o(i.MFRC522_AntennaOn) for MFRC522_AntennaOn
    card_manager.o(i.CARD_Proc) refers to mfrc522.o(i.MFRC522_Anticoll) for MFRC522_Anticoll
    card_manager.o(i.CARD_Proc) refers to card_manager.o(i.CARD_find_card) for CARD_find_card
    card_manager.o(i.CARD_Proc) refers to card_manager.o(i.CARD_add_card) for CARD_add_card
    card_manager.o(i.CARD_Proc) refers to mfrc522.o(i.MFRC522_Halt) for MFRC522_Halt
    card_manager.o(i.CARD_Proc) refers to card_manager.o(.data) for error_count
    card_manager.o(i.CARD_Proc) refers to card_manager.o(.bss) for card_arr
    card_manager.o(i.CARD_Proc) refers to card_manager.o(.conststring) for .conststring
    card_manager.o(i.CARD_add_card) refers to memseta.o(.text) for __aeabi_memclr
    card_manager.o(i.CARD_add_card) refers to memcpya.o(.text) for __aeabi_memcpy
    card_manager.o(i.CARD_add_card) refers to card_manager.o(.bss) for card_arr
    card_manager.o(i.CARD_find_card) refers to memcmp.o(.text) for memcmp
    card_manager.o(i.CARD_find_card) refers to card_manager.o(.bss) for card_arr
    delay.o(i.delay_init) refers to misc.o(i.SysTick_CLKSourceConfig) for SysTick_CLKSourceConfig
    delay.o(i.delay_init) refers to delay.o(.data) for fac_us
    delay.o(i.delay_ms) refers to delay.o(.data) for fac_ms
    delay.o(i.delay_us) refers to delay.o(.data) for fac_us
    usart.o(i.USART1_Config) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart.o(i.USART1_Config) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    usart.o(i.USART1_Config) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    usart.o(i.USART1_Config) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    usart.o(i.USART1_printf) refers to stm32f10x_usart.o(i.USART_SendData) for USART_SendData
    usart.o(i.USART1_printf) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart.o(i.USART1_printf) refers to usart.o(i.int_to_str) for int_to_str
    usart.o(i.fputc) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart.o(i.fputc) refers to stm32f10x_usart.o(i.USART_SendData) for USART_SendData
    startup_stm32f10x_md.o(RESET) refers to startup_stm32f10x_md.o(STACK) for __initial_sp
    startup_stm32f10x_md.o(RESET) refers to startup_stm32f10x_md.o(.text) for Reset_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f10x_md.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_usart.o(i.USART_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_gpio.o(i.GPIO_AFIODeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(i.GPIO_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_rcc.o(i.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.data) for APBAHBPrescTable
    stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f10x_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f10x_adc.o(i.ADC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f10x_md.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f10x_md.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr


==============================================================================

Removing Unused input sections from the image.

    Removing system_stm32f10x.o(i.SystemCoreClockUpdate), (164 bytes).
    Removing system_stm32f10x.o(.data), (20 bytes).
    Removing led.o(i.LED_Off), (20 bytes).
    Removing led.o(i.LED_Toggle), (36 bytes).
    Removing led.o(i.LED_Twinkle), (18 bytes).
    Removing oled.o(i.IIC_delay), (16 bytes).
    Removing oled.o(i.OLED_Clear), (48 bytes).
    Removing oled.o(i.OLED_ColorTurn), (28 bytes).
    Removing oled.o(i.OLED_DisPlay_Off), (28 bytes).
    Removing oled.o(i.OLED_DisPlay_On), (28 bytes).
    Removing oled.o(i.OLED_DisplayTurn), (44 bytes).
    Removing oled.o(i.OLED_DrawCircle), (168 bytes).
    Removing oled.o(i.OLED_DrawLine), (172 bytes).
    Removing oled.o(i.OLED_DrawPoint), (120 bytes).
    Removing oled.o(i.OLED_I2C_Start), (60 bytes).
    Removing oled.o(i.OLED_I2C_Stop), (44 bytes).
    Removing oled.o(i.OLED_I2C_WaitAck), (52 bytes).
    Removing oled.o(i.OLED_Init), (300 bytes).
    Removing oled.o(i.OLED_Pow), (22 bytes).
    Removing oled.o(i.OLED_Refresh), (104 bytes).
    Removing oled.o(i.OLED_ScrollDisplay), (172 bytes).
    Removing oled.o(i.OLED_Send_Byte), (84 bytes).
    Removing oled.o(i.OLED_ShowChar), (320 bytes).
    Removing oled.o(i.OLED_ShowChinese), (264 bytes).
    Removing oled.o(i.OLED_ShowNum), (148 bytes).
    Removing oled.o(i.OLED_ShowPicture), (218 bytes).
    Removing oled.o(i.OLED_ShowString), (78 bytes).
    Removing oled.o(i.OLED_WR_Byte), (56 bytes).
    Removing oled.o(.bss), (1152 bytes).
    Removing oled.o(.constdata), (7408 bytes).
    Removing mfrc522.o(i.MFRC522_AuthState), (112 bytes).
    Removing mfrc522.o(i.MFRC522_Read), (86 bytes).
    Removing mfrc522.o(i.MFRC522_SelectTag), (114 bytes).
    Removing mfrc522.o(i.MFRC522_Write), (152 bytes).
    Removing card_manager.o(i.CARD_uchar_to_uint), (28 bytes).
    Removing usart.o(i.USART1_printf), (262 bytes).
    Removing usart.o(i.int_to_str), (144 bytes).
    Removing sys.o(.emb_text), (6 bytes).
    Removing sys.o(i.INTX_DISABLE), (4 bytes).
    Removing sys.o(i.INTX_ENABLE), (4 bytes).
    Removing sys.o(i.WFI_SET), (4 bytes).
    Removing core_cm3.o(.emb_text), (32 bytes).
    Removing startup_stm32f10x_md.o(HEAP), (0 bytes).
    Removing misc.o(i.NVIC_Init), (112 bytes).
    Removing misc.o(i.NVIC_PriorityGroupConfig), (20 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (32 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearFlag), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearITPendingBit), (30 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockInit), (34 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f10x_usart.o(i.USART_DMACmd), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_DeInit), (156 bytes).
    Removing stm32f10x_usart.o(i.USART_GetITStatus), (84 bytes).
    Removing stm32f10x_usart.o(i.USART_HalfDuplexCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_ITConfig), (74 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDACmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDAConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINBreakDetectLengthConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OneBitMethodCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OverSampling8Cmd), (22 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiveData), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiverWakeUpCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SendBreak), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_SetAddress), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SetPrescaler), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardNACKCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_StructInit), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_WakeUpConfig), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_AFIODeInit), (20 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_DeInit), (200 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ETH_MediaInterfaceConfig), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EXTILineConfig), (64 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputCmd), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputConfig), (32 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinLockConfig), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinRemapConfig), (144 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_StructInit), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_WriteBit), (10 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ADCCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AdjustHSICalibrationValue), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearFlag), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_DeInit), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetFlagStatus), (60 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetITStatus), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSEConfig), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ITConfig), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSEConfig), (52 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_MCOConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK1Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK2Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLConfig), (28 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKConfig), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_SYSCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_USBCLKConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp), (56 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogSingleChannelConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogThresholdsConfig), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_AutoInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearFlag), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearITPendingBit), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_Cmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_DMACmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_DeInit), (92 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeChannelCountConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetCalibrationStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetConversionValue), (8 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetDualModeConversionValue), (12 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetFlagStatus), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetITStatus), (36 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetInjectedConversionValue), (28 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartConvStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartInjectedConvCmdStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_ITConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_Init), (80 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedChannelConfig), (130 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedDiscModeCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedSequencerLengthConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_RegularChannelConfig), (184 bytes).
    Removing stm32f10x_adc.o(i.ADC_ResetCalibration), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_SetInjectedOffset), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_StartCalibration), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_StructInit), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_TempSensorVrefintCmd), (36 bytes).
    Removing dadd.o(.text), (334 bytes).
    Removing dmul.o(.text), (228 bytes).
    Removing ddiv.o(.text), (222 bytes).
    Removing dfixul.o(.text), (48 bytes).
    Removing cdrcmple.o(.text), (48 bytes).
    Removing depilogue.o(.text), (186 bytes).

158 unused section(s) (total 16862 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcmp.c         0x00000000   Number         0  memcmp.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strcpy.c         0x00000000   Number         0  strcpy.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ..\CORE\core_cm3.c                       0x00000000   Number         0  core_cm3.o ABSOLUTE
    ..\CORE\startup_stm32f10x_md.s           0x00000000   Number         0  startup_stm32f10x_md.o ABSOLUTE
    ..\HARDWARE\CARD_MANAGER\card_manager.c  0x00000000   Number         0  card_manager.o ABSOLUTE
    ..\HARDWARE\LED\led.c                    0x00000000   Number         0  led.o ABSOLUTE
    ..\HARDWARE\MFRC522\MFRC522.c            0x00000000   Number         0  mfrc522.o ABSOLUTE
    ..\HARDWARE\OLED\oled.c                  0x00000000   Number         0  oled.o ABSOLUTE
    ..\STM32F10x_FWLib\src\misc.c            0x00000000   Number         0  misc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_adc.c   0x00000000   Number         0  stm32f10x_adc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_gpio.c  0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_rcc.c   0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_usart.c 0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    ..\SYSTEM\delay\delay.c                  0x00000000   Number         0  delay.o ABSOLUTE
    ..\SYSTEM\sys\sys.c                      0x00000000   Number         0  sys.o ABSOLUTE
    ..\SYSTEM\usart\usart.c                  0x00000000   Number         0  usart.o ABSOLUTE
    ..\\CORE\\core_cm3.c                     0x00000000   Number         0  core_cm3.o ABSOLUTE
    ..\\SYSTEM\\sys\\sys.c                   0x00000000   Number         0  sys.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    stm32f10x_it.c                           0x00000000   Number         0  stm32f10x_it.o ABSOLUTE
    system_stm32f10x.c                       0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    RESET                                    0x08000000   Section      236  startup_stm32f10x_md.o(RESET)
    .ARM.Collect$$$$00000000                 0x080000ec   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x080000ec   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x080000f0   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x080000f4   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x080000f4   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x080000f4   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000E                 0x080000fc   Section        4  entry12b.o(.ARM.Collect$$$$0000000E)
    .ARM.Collect$$$$0000000F                 0x08000100   Section        0  entry10a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00000011                 0x08000100   Section        0  entry11a.o(.ARM.Collect$$$$00000011)
    .ARM.Collect$$$$00002712                 0x08000100   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x08000100   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x08000104   Section       28  startup_stm32f10x_md.o(.text)
    .text                                    0x08000120   Section        0  memcpya.o(.text)
    .text                                    0x08000144   Section        0  memseta.o(.text)
    .text                                    0x08000168   Section        0  memcmp.o(.text)
    .text                                    0x08000182   Section        0  strcpy.o(.text)
    .text                                    0x08000194   Section        0  uldiv.o(.text)
    .text                                    0x080001f8   Section       36  init.o(.text)
    .text                                    0x0800021c   Section        0  llshl.o(.text)
    .text                                    0x0800023a   Section        0  llushr.o(.text)
    i.BusFault_Handler                       0x0800025a   Section        0  stm32f10x_it.o(i.BusFault_Handler)
    i.CARD_InitDatabase                      0x08000260   Section        0  card_manager.o(i.CARD_InitDatabase)
    i.CARD_Proc                              0x080003a4   Section        0  card_manager.o(i.CARD_Proc)
    i.CARD_add_card                          0x08000680   Section        0  card_manager.o(i.CARD_add_card)
    i.CARD_find_card                         0x08000700   Section        0  card_manager.o(i.CARD_find_card)
    i.CalulateCRC                            0x0800074c   Section        0  mfrc522.o(i.CalulateCRC)
    i.ClearBitMask                           0x080007b8   Section        0  mfrc522.o(i.ClearBitMask)
    i.DebugMon_Handler                       0x080007d4   Section        0  stm32f10x_it.o(i.DebugMon_Handler)
    i.GPIO_Init                              0x080007d6   Section        0  stm32f10x_gpio.o(i.GPIO_Init)
    i.GPIO_ReadInputDataBit                  0x080008ec   Section        0  stm32f10x_gpio.o(i.GPIO_ReadInputDataBit)
    i.GPIO_ResetBits                         0x080008fe   Section        0  stm32f10x_gpio.o(i.GPIO_ResetBits)
    i.GPIO_SetBits                           0x08000902   Section        0  stm32f10x_gpio.o(i.GPIO_SetBits)
    i.HardFault_Handler                      0x08000906   Section        0  stm32f10x_it.o(i.HardFault_Handler)
    i.LED_Init                               0x0800090c   Section        0  led.o(i.LED_Init)
    i.LED_On                                 0x08000940   Section        0  led.o(i.LED_On)
    i.MFRC522_AntennaOff                     0x08000954   Section        0  mfrc522.o(i.MFRC522_AntennaOff)
    i.MFRC522_AntennaOn                      0x08000960   Section        0  mfrc522.o(i.MFRC522_AntennaOn)
    i.MFRC522_Anticoll                       0x08000aa8   Section        0  mfrc522.o(i.MFRC522_Anticoll)
    i.MFRC522_Halt                           0x08000b1a   Section        0  mfrc522.o(i.MFRC522_Halt)
    i.MFRC522_Init                           0x08000b50   Section        0  mfrc522.o(i.MFRC522_Init)
    i.MFRC522_Request                        0x08000fac   Section        0  mfrc522.o(i.MFRC522_Request)
    i.MFRC522_Reset                          0x08001144   Section        0  mfrc522.o(i.MFRC522_Reset)
    i.MFRC522_ToCard                         0x08001188   Section        0  mfrc522.o(i.MFRC522_ToCard)
    i.MemManage_Handler                      0x080014b4   Section        0  stm32f10x_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x080014b8   Section        0  stm32f10x_it.o(i.NMI_Handler)
    i.PendSV_Handler                         0x080014ba   Section        0  stm32f10x_it.o(i.PendSV_Handler)
    i.RCC_APB2PeriphClockCmd                 0x080014bc   Section        0  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_GetClocksFreq                      0x080014dc   Section        0  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    i.Read_MFRC522                           0x080015b0   Section        0  mfrc522.o(i.Read_MFRC522)
    i.SVC_Handler                            0x08001668   Section        0  stm32f10x_it.o(i.SVC_Handler)
    i.SetBitMask                             0x0800166a   Section        0  mfrc522.o(i.SetBitMask)
    i.SetSysClock                            0x08001686   Section        0  system_stm32f10x.o(i.SetSysClock)
    SetSysClock                              0x08001687   Thumb Code     8  system_stm32f10x.o(i.SetSysClock)
    i.SetSysClockTo72                        0x08001690   Section        0  system_stm32f10x.o(i.SetSysClockTo72)
    SetSysClockTo72                          0x08001691   Thumb Code   214  system_stm32f10x.o(i.SetSysClockTo72)
    i.SysTick_CLKSourceConfig                0x08001770   Section        0  misc.o(i.SysTick_CLKSourceConfig)
    i.SysTick_Handler                        0x08001798   Section        0  stm32f10x_it.o(i.SysTick_Handler)
    i.SystemInit                             0x0800179c   Section        0  system_stm32f10x.o(i.SystemInit)
    i.USART1_Config                          0x080017fc   Section        0  usart.o(i.USART1_Config)
    i.USART_Cmd                              0x08001878   Section        0  stm32f10x_usart.o(i.USART_Cmd)
    i.USART_GetFlagStatus                    0x08001890   Section        0  stm32f10x_usart.o(i.USART_GetFlagStatus)
    i.USART_Init                             0x080018ac   Section        0  stm32f10x_usart.o(i.USART_Init)
    i.USART_SendData                         0x08001984   Section        0  stm32f10x_usart.o(i.USART_SendData)
    i.UsageFault_Handler                     0x0800198c   Section        0  stm32f10x_it.o(i.UsageFault_Handler)
    i.Write_MFRC522                          0x08001990   Section        0  mfrc522.o(i.Write_MFRC522)
    i.__0printf$8                            0x08001a50   Section        0  printf8.o(i.__0printf$8)
    i.__scatterload_copy                     0x08001a70   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x08001a7e   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08001a80   Section       14  handlers.o(i.__scatterload_zeroinit)
    i._printf_core                           0x08001a90   Section        0  printf8.o(i._printf_core)
    _printf_core                             0x08001a91   Thumb Code   984  printf8.o(i._printf_core)
    i._printf_post_padding                   0x08001e94   Section        0  printf8.o(i._printf_post_padding)
    _printf_post_padding                     0x08001e95   Thumb Code    36  printf8.o(i._printf_post_padding)
    i._printf_pre_padding                    0x08001eb8   Section        0  printf8.o(i._printf_pre_padding)
    _printf_pre_padding                      0x08001eb9   Thumb Code    46  printf8.o(i._printf_pre_padding)
    i.delay_init                             0x08001ee8   Section        0  delay.o(i.delay_init)
    i.delay_ms                               0x08001f24   Section        0  delay.o(i.delay_ms)
    i.delay_us                               0x08001f60   Section        0  delay.o(i.delay_us)
    i.fputc                                  0x08001f9c   Section        0  usart.o(i.fputc)
    i.main                                   0x08001fc0   Section        0  main.o(i.main)
    .conststring                             0x0800200c   Section      290  mfrc522.o(.conststring)
    .conststring                             0x08002130   Section       71  card_manager.o(.conststring)
    .data                                    0x20000000   Section        2  mfrc522.o(.data)
    tocard_debug_count                       0x20000000   Data           1  mfrc522.o(.data)
    debug_count                              0x20000001   Data           1  mfrc522.o(.data)
    .data                                    0x20000002   Section        2  card_manager.o(.data)
    error_count                              0x20000003   Data           1  card_manager.o(.data)
    .data                                    0x20000004   Section        4  delay.o(.data)
    fac_us                                   0x20000004   Data           1  delay.o(.data)
    fac_ms                                   0x20000006   Data           2  delay.o(.data)
    .data                                    0x20000008   Section       20  stm32f10x_rcc.o(.data)
    APBAHBPrescTable                         0x20000008   Data          16  stm32f10x_rcc.o(.data)
    ADCPrescTable                            0x20000018   Data           4  stm32f10x_rcc.o(.data)
    .data                                    0x2000001c   Section        4  stdout.o(.data)
    .bss                                     0x20000020   Section     2560  card_manager.o(.bss)
    STACK                                    0x20000a20   Section      512  startup_stm32f10x_md.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __arm_fini_                               - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x000000ec   Number         0  startup_stm32f10x_md.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_md.o(RESET)
    __Vectors_End                            0x080000ec   Data           0  startup_stm32f10x_md.o(RESET)
    __main                                   0x080000ed   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x080000ed   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x080000f1   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x080000f5   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x080000f5   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x080000f5   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x080000f5   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_lib_shutdown_fini                   0x080000fd   Thumb Code     0  entry12b.o(.ARM.Collect$$$$0000000E)
    __rt_final_cpp                           0x08000101   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000F)
    __rt_final_exit                          0x08000101   Thumb Code     0  entry11a.o(.ARM.Collect$$$$00000011)
    Reset_Handler                            0x08000105   Thumb Code     4  startup_stm32f10x_md.o(.text)
    ADC1_2_IRQHandler                        0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    CAN1_RX1_IRQHandler                      0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    CAN1_SCE_IRQHandler                      0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel1_IRQHandler                 0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel2_IRQHandler                 0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel3_IRQHandler                 0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel4_IRQHandler                 0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel5_IRQHandler                 0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel6_IRQHandler                 0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel7_IRQHandler                 0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI0_IRQHandler                         0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI15_10_IRQHandler                     0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI1_IRQHandler                         0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI2_IRQHandler                         0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI3_IRQHandler                         0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI4_IRQHandler                         0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI9_5_IRQHandler                       0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    FLASH_IRQHandler                         0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C1_ER_IRQHandler                       0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C1_EV_IRQHandler                       0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C2_ER_IRQHandler                       0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C2_EV_IRQHandler                       0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    PVD_IRQHandler                           0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RCC_IRQHandler                           0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RTCAlarm_IRQHandler                      0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RTC_IRQHandler                           0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    SPI1_IRQHandler                          0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    SPI2_IRQHandler                          0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TAMPER_IRQHandler                        0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_BRK_IRQHandler                      0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_CC_IRQHandler                       0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_UP_IRQHandler                       0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM2_IRQHandler                          0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM3_IRQHandler                          0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM4_IRQHandler                          0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USART1_IRQHandler                        0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USART2_IRQHandler                        0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USART3_IRQHandler                        0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USBWakeUp_IRQHandler                     0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    WWDG_IRQHandler                          0x0800011b   Thumb Code     0  startup_stm32f10x_md.o(.text)
    __aeabi_memcpy                           0x08000121   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x08000121   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x08000121   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x08000145   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x08000145   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x08000145   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x08000153   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x08000153   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x08000153   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x08000157   Thumb Code    18  memseta.o(.text)
    memcmp                                   0x08000169   Thumb Code    26  memcmp.o(.text)
    strcpy                                   0x08000183   Thumb Code    18  strcpy.o(.text)
    __aeabi_uldivmod                         0x08000195   Thumb Code    98  uldiv.o(.text)
    __scatterload                            0x080001f9   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x080001f9   Thumb Code     0  init.o(.text)
    __aeabi_llsl                             0x0800021d   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x0800021d   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x0800023b   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x0800023b   Thumb Code     0  llushr.o(.text)
    BusFault_Handler                         0x0800025b   Thumb Code     4  stm32f10x_it.o(i.BusFault_Handler)
    CARD_InitDatabase                        0x08000261   Thumb Code   194  card_manager.o(i.CARD_InitDatabase)
    CARD_Proc                                0x080003a5   Thumb Code   320  card_manager.o(i.CARD_Proc)
    CARD_add_card                            0x08000681   Thumb Code   124  card_manager.o(i.CARD_add_card)
    CARD_find_card                           0x08000701   Thumb Code    70  card_manager.o(i.CARD_find_card)
    CalulateCRC                              0x0800074d   Thumb Code   108  mfrc522.o(i.CalulateCRC)
    ClearBitMask                             0x080007b9   Thumb Code    28  mfrc522.o(i.ClearBitMask)
    DebugMon_Handler                         0x080007d5   Thumb Code     2  stm32f10x_it.o(i.DebugMon_Handler)
    GPIO_Init                                0x080007d7   Thumb Code   278  stm32f10x_gpio.o(i.GPIO_Init)
    GPIO_ReadInputDataBit                    0x080008ed   Thumb Code    18  stm32f10x_gpio.o(i.GPIO_ReadInputDataBit)
    GPIO_ResetBits                           0x080008ff   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_ResetBits)
    GPIO_SetBits                             0x08000903   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_SetBits)
    HardFault_Handler                        0x08000907   Thumb Code     4  stm32f10x_it.o(i.HardFault_Handler)
    LED_Init                                 0x0800090d   Thumb Code    48  led.o(i.LED_Init)
    LED_On                                   0x08000941   Thumb Code    14  led.o(i.LED_On)
    MFRC522_AntennaOff                       0x08000955   Thumb Code    12  mfrc522.o(i.MFRC522_AntennaOff)
    MFRC522_AntennaOn                        0x08000961   Thumb Code    88  mfrc522.o(i.MFRC522_AntennaOn)
    MFRC522_Anticoll                         0x08000aa9   Thumb Code   114  mfrc522.o(i.MFRC522_Anticoll)
    MFRC522_Halt                             0x08000b1b   Thumb Code    52  mfrc522.o(i.MFRC522_Halt)
    MFRC522_Init                             0x08000b51   Thumb Code   464  mfrc522.o(i.MFRC522_Init)
    MFRC522_Request                          0x08000fad   Thumb Code   174  mfrc522.o(i.MFRC522_Request)
    MFRC522_Reset                            0x08001145   Thumb Code    68  mfrc522.o(i.MFRC522_Reset)
    MFRC522_ToCard                           0x08001189   Thumb Code   446  mfrc522.o(i.MFRC522_ToCard)
    MemManage_Handler                        0x080014b5   Thumb Code     4  stm32f10x_it.o(i.MemManage_Handler)
    NMI_Handler                              0x080014b9   Thumb Code     2  stm32f10x_it.o(i.NMI_Handler)
    PendSV_Handler                           0x080014bb   Thumb Code     2  stm32f10x_it.o(i.PendSV_Handler)
    RCC_APB2PeriphClockCmd                   0x080014bd   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_GetClocksFreq                        0x080014dd   Thumb Code   192  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    Read_MFRC522                             0x080015b1   Thumb Code   180  mfrc522.o(i.Read_MFRC522)
    SVC_Handler                              0x08001669   Thumb Code     2  stm32f10x_it.o(i.SVC_Handler)
    SetBitMask                               0x0800166b   Thumb Code    28  mfrc522.o(i.SetBitMask)
    SysTick_CLKSourceConfig                  0x08001771   Thumb Code    40  misc.o(i.SysTick_CLKSourceConfig)
    SysTick_Handler                          0x08001799   Thumb Code     2  stm32f10x_it.o(i.SysTick_Handler)
    SystemInit                               0x0800179d   Thumb Code    78  system_stm32f10x.o(i.SystemInit)
    USART1_Config                            0x080017fd   Thumb Code   114  usart.o(i.USART1_Config)
    USART_Cmd                                0x08001879   Thumb Code    24  stm32f10x_usart.o(i.USART_Cmd)
    USART_GetFlagStatus                      0x08001891   Thumb Code    26  stm32f10x_usart.o(i.USART_GetFlagStatus)
    USART_Init                               0x080018ad   Thumb Code   210  stm32f10x_usart.o(i.USART_Init)
    USART_SendData                           0x08001985   Thumb Code     8  stm32f10x_usart.o(i.USART_SendData)
    UsageFault_Handler                       0x0800198d   Thumb Code     4  stm32f10x_it.o(i.UsageFault_Handler)
    Write_MFRC522                            0x08001991   Thumb Code   186  mfrc522.o(i.Write_MFRC522)
    __0printf$8                              0x08001a51   Thumb Code    22  printf8.o(i.__0printf$8)
    __1printf$8                              0x08001a51   Thumb Code     0  printf8.o(i.__0printf$8)
    __2printf                                0x08001a51   Thumb Code     0  printf8.o(i.__0printf$8)
    __scatterload_copy                       0x08001a71   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x08001a7f   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08001a81   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    delay_init                               0x08001ee9   Thumb Code    52  delay.o(i.delay_init)
    delay_ms                                 0x08001f25   Thumb Code    56  delay.o(i.delay_ms)
    delay_us                                 0x08001f61   Thumb Code    56  delay.o(i.delay_us)
    fputc                                    0x08001f9d   Thumb Code    32  usart.o(i.fputc)
    main                                     0x08001fc1   Thumb Code    60  main.o(i.main)
    Region$$Table$$Base                      0x08002178   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08002198   Number         0  anon$$obj.o(Region$$Table)
    MFRC522_flag                             0x20000002   Data           1  card_manager.o(.data)
    __stdout                                 0x2000001c   Data           4  stdout.o(.data)
    card_arr                                 0x20000020   Data        2560  card_manager.o(.bss)
    __initial_sp                             0x20000c20   Data           0  startup_stm32f10x_md.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080000ed

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x000021b8, Max: 0x00010000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00002198, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000000ec   Data   RO          708    RESET               startup_stm32f10x_md.o
    0x080000ec   0x080000ec   0x00000000   Code   RO         1465  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x080000ec   0x080000ec   0x00000004   Code   RO         1739    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x080000f0   0x080000f0   0x00000004   Code   RO         1742    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x080000f4   0x080000f4   0x00000000   Code   RO         1744    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x080000f4   0x080000f4   0x00000000   Code   RO         1746    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x080000f4   0x080000f4   0x00000008   Code   RO         1747    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x080000fc   0x080000fc   0x00000004   Code   RO         1754    .ARM.Collect$$$$0000000E  mc_w.l(entry12b.o)
    0x08000100   0x08000100   0x00000000   Code   RO         1749    .ARM.Collect$$$$0000000F  mc_w.l(entry10a.o)
    0x08000100   0x08000100   0x00000000   Code   RO         1751    .ARM.Collect$$$$00000011  mc_w.l(entry11a.o)
    0x08000100   0x08000100   0x00000004   Code   RO         1740    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x08000104   0x08000104   0x0000001c   Code   RO          709    .text               startup_stm32f10x_md.o
    0x08000120   0x08000120   0x00000024   Code   RO         1468    .text               mc_w.l(memcpya.o)
    0x08000144   0x08000144   0x00000024   Code   RO         1470    .text               mc_w.l(memseta.o)
    0x08000168   0x08000168   0x0000001a   Code   RO         1472    .text               mc_w.l(memcmp.o)
    0x08000182   0x08000182   0x00000012   Code   RO         1474    .text               mc_w.l(strcpy.o)
    0x08000194   0x08000194   0x00000062   Code   RO         1758    .text               mc_w.l(uldiv.o)
    0x080001f6   0x080001f6   0x00000002   PAD
    0x080001f8   0x080001f8   0x00000024   Code   RO         1771    .text               mc_w.l(init.o)
    0x0800021c   0x0800021c   0x0000001e   Code   RO         1773    .text               mc_w.l(llshl.o)
    0x0800023a   0x0800023a   0x00000020   Code   RO         1775    .text               mc_w.l(llushr.o)
    0x0800025a   0x0800025a   0x00000004   Code   RO            1    i.BusFault_Handler  stm32f10x_it.o
    0x0800025e   0x0800025e   0x00000002   PAD
    0x08000260   0x08000260   0x00000144   Code   RO          560    i.CARD_InitDatabase  card_manager.o
    0x080003a4   0x080003a4   0x000002dc   Code   RO          561    i.CARD_Proc         card_manager.o
    0x08000680   0x08000680   0x00000080   Code   RO          562    i.CARD_add_card     card_manager.o
    0x08000700   0x08000700   0x0000004c   Code   RO          563    i.CARD_find_card    card_manager.o
    0x0800074c   0x0800074c   0x0000006c   Code   RO          450    i.CalulateCRC       mfrc522.o
    0x080007b8   0x080007b8   0x0000001c   Code   RO          451    i.ClearBitMask      mfrc522.o
    0x080007d4   0x080007d4   0x00000002   Code   RO            2    i.DebugMon_Handler  stm32f10x_it.o
    0x080007d6   0x080007d6   0x00000116   Code   RO          935    i.GPIO_Init         stm32f10x_gpio.o
    0x080008ec   0x080008ec   0x00000012   Code   RO          939    i.GPIO_ReadInputDataBit  stm32f10x_gpio.o
    0x080008fe   0x080008fe   0x00000004   Code   RO          942    i.GPIO_ResetBits    stm32f10x_gpio.o
    0x08000902   0x08000902   0x00000004   Code   RO          943    i.GPIO_SetBits      stm32f10x_gpio.o
    0x08000906   0x08000906   0x00000004   Code   RO            3    i.HardFault_Handler  stm32f10x_it.o
    0x0800090a   0x0800090a   0x00000002   PAD
    0x0800090c   0x0800090c   0x00000034   Code   RO          234    i.LED_Init          led.o
    0x08000940   0x08000940   0x00000014   Code   RO          236    i.LED_On            led.o
    0x08000954   0x08000954   0x0000000c   Code   RO          452    i.MFRC522_AntennaOff  mfrc522.o
    0x08000960   0x08000960   0x00000148   Code   RO          453    i.MFRC522_AntennaOn  mfrc522.o
    0x08000aa8   0x08000aa8   0x00000072   Code   RO          454    i.MFRC522_Anticoll  mfrc522.o
    0x08000b1a   0x08000b1a   0x00000034   Code   RO          456    i.MFRC522_Halt      mfrc522.o
    0x08000b4e   0x08000b4e   0x00000002   PAD
    0x08000b50   0x08000b50   0x0000045c   Code   RO          457    i.MFRC522_Init      mfrc522.o
    0x08000fac   0x08000fac   0x00000198   Code   RO          459    i.MFRC522_Request   mfrc522.o
    0x08001144   0x08001144   0x00000044   Code   RO          460    i.MFRC522_Reset     mfrc522.o
    0x08001188   0x08001188   0x0000032c   Code   RO          462    i.MFRC522_ToCard    mfrc522.o
    0x080014b4   0x080014b4   0x00000004   Code   RO            4    i.MemManage_Handler  stm32f10x_it.o
    0x080014b8   0x080014b8   0x00000002   Code   RO            5    i.NMI_Handler       stm32f10x_it.o
    0x080014ba   0x080014ba   0x00000002   Code   RO            6    i.PendSV_Handler    stm32f10x_it.o
    0x080014bc   0x080014bc   0x00000020   Code   RO         1047    i.RCC_APB2PeriphClockCmd  stm32f10x_rcc.o
    0x080014dc   0x080014dc   0x000000d4   Code   RO         1055    i.RCC_GetClocksFreq  stm32f10x_rcc.o
    0x080015b0   0x080015b0   0x000000b8   Code   RO          464    i.Read_MFRC522      mfrc522.o
    0x08001668   0x08001668   0x00000002   Code   RO            7    i.SVC_Handler       stm32f10x_it.o
    0x0800166a   0x0800166a   0x0000001c   Code   RO          465    i.SetBitMask        mfrc522.o
    0x08001686   0x08001686   0x00000008   Code   RO          152    i.SetSysClock       system_stm32f10x.o
    0x0800168e   0x0800168e   0x00000002   PAD
    0x08001690   0x08001690   0x000000e0   Code   RO          153    i.SetSysClockTo72   system_stm32f10x.o
    0x08001770   0x08001770   0x00000028   Code   RO          717    i.SysTick_CLKSourceConfig  misc.o
    0x08001798   0x08001798   0x00000002   Code   RO            8    i.SysTick_Handler   stm32f10x_it.o
    0x0800179a   0x0800179a   0x00000002   PAD
    0x0800179c   0x0800179c   0x00000060   Code   RO          155    i.SystemInit        system_stm32f10x.o
    0x080017fc   0x080017fc   0x0000007c   Code   RO          630    i.USART1_Config     usart.o
    0x08001878   0x08001878   0x00000018   Code   RO          753    i.USART_Cmd         stm32f10x_usart.o
    0x08001890   0x08001890   0x0000001a   Code   RO          756    i.USART_GetFlagStatus  stm32f10x_usart.o
    0x080018aa   0x080018aa   0x00000002   PAD
    0x080018ac   0x080018ac   0x000000d8   Code   RO          760    i.USART_Init        stm32f10x_usart.o
    0x08001984   0x08001984   0x00000008   Code   RO          770    i.USART_SendData    stm32f10x_usart.o
    0x0800198c   0x0800198c   0x00000004   Code   RO            9    i.UsageFault_Handler  stm32f10x_it.o
    0x08001990   0x08001990   0x000000c0   Code   RO          466    i.Write_MFRC522     mfrc522.o
    0x08001a50   0x08001a50   0x00000020   Code   RO         1685    i.__0printf$8       mc_w.l(printf8.o)
    0x08001a70   0x08001a70   0x0000000e   Code   RO         1783    i.__scatterload_copy  mc_w.l(handlers.o)
    0x08001a7e   0x08001a7e   0x00000002   Code   RO         1784    i.__scatterload_null  mc_w.l(handlers.o)
    0x08001a80   0x08001a80   0x0000000e   Code   RO         1785    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x08001a8e   0x08001a8e   0x00000002   PAD
    0x08001a90   0x08001a90   0x00000404   Code   RO         1692    i._printf_core      mc_w.l(printf8.o)
    0x08001e94   0x08001e94   0x00000024   Code   RO         1693    i._printf_post_padding  mc_w.l(printf8.o)
    0x08001eb8   0x08001eb8   0x0000002e   Code   RO         1694    i._printf_pre_padding  mc_w.l(printf8.o)
    0x08001ee6   0x08001ee6   0x00000002   PAD
    0x08001ee8   0x08001ee8   0x0000003c   Code   RO          604    i.delay_init        delay.o
    0x08001f24   0x08001f24   0x0000003c   Code   RO          605    i.delay_ms          delay.o
    0x08001f60   0x08001f60   0x0000003c   Code   RO          606    i.delay_us          delay.o
    0x08001f9c   0x08001f9c   0x00000024   Code   RO          632    i.fputc             usart.o
    0x08001fc0   0x08001fc0   0x0000004c   Code   RO          186    i.main              main.o
    0x0800200c   0x0800200c   0x00000122   Data   RO          467    .conststring        mfrc522.o
    0x0800212e   0x0800212e   0x00000002   PAD
    0x08002130   0x08002130   0x00000047   Data   RO          566    .conststring        card_manager.o
    0x08002177   0x08002177   0x00000001   PAD
    0x08002178   0x08002178   0x00000020   Data   RO         1781    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08002198, Size: 0x00000c20, Max: 0x00005000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08002198   0x00000002   Data   RW          468    .data               mfrc522.o
    0x20000002   0x0800219a   0x00000002   Data   RW          567    .data               card_manager.o
    0x20000004   0x0800219c   0x00000004   Data   RW          607    .data               delay.o
    0x20000008   0x080021a0   0x00000014   Data   RW         1075    .data               stm32f10x_rcc.o
    0x2000001c   0x080021b4   0x00000004   Data   RW         1755    .data               mc_w.l(stdout.o)
    0x20000020        -       0x00000a00   Zero   RW          565    .bss                card_manager.o
    0x20000a20        -       0x00000200   Zero   RW          706    STACK               startup_stm32f10x_md.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

      1260        552         71          2       2560       4489   card_manager.o
       180         16          0          4          0       1939   delay.o
        72         10          0          0          0     202748   led.o
        76         16          0          0          0        495   main.o
      3450       1502        290          2          0      10114   mfrc522.o
        40          0          0          0          0        570   misc.o
        28          4        236          0        512        820   startup_stm32f10x_md.o
       304          0          0          0          0       3884   stm32f10x_gpio.o
        26          0          0          0          0      31914   stm32f10x_it.o
       244         26          0         20          0       4049   stm32f10x_rcc.o
       274          6          0          0          0       4244   stm32f10x_usart.o
         0          0          0          0          0         32   sys.o
       328         28          0          0          0       1985   system_stm32f10x.o
       160         14          0          0          0       3036   usart.o

    ----------------------------------------------------------------------
      6454       <USER>        <GROUP>         28       3072     270319   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        12          0          3          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         4          0          0          0          0          0   entry12b.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
        30          0          0          0          0         68   llshl.o
        32          0          0          0          0         68   llushr.o
        26          0          0          0          0         80   memcmp.o
        36          0          0          0          0         68   memcpya.o
        36          0          0          0          0        108   memseta.o
      1142         54          0          0          0        352   printf8.o
         0          0          0          4          0          0   stdout.o
        18          0          0          0          0         68   strcpy.o
        98          0          0          0          0         92   uldiv.o

    ----------------------------------------------------------------------
      1514         <USER>          <GROUP>          4          0        972   Library Totals
         6          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      1508         70          0          4          0        972   mc_w.l

    ----------------------------------------------------------------------
      1514         <USER>          <GROUP>          4          0        972   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      7968       2244        632         32       3072     268635   Grand Totals
      7968       2244        632         32       3072     268635   ELF Image Totals
      7968       2244        632         32          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                 8600 (   8.40kB)
    Total RW  Size (RW Data + ZI Data)              3104 (   3.03kB)
    Total ROM Size (Code + RO Data + RW Data)       8632 (   8.43kB)

==============================================================================

