


ARM Macro Assembler    Page 1 


    1 00000000         ;******************** (C) COPYRIGHT 2009 STMicroelectron
                       ics ********************
    2 00000000         ;* File Name          : startup_stm32f10x_md.s
    3 00000000         ;* Author             : MCD Application Team
    4 00000000         ;* Version            : V3.0.0
    5 00000000         ;* Date               : 04/06/2009
    6 00000000         ;* Description        : STM32F10x Medium Density Devices
                        vector table for RVMDK 
    7 00000000         ;*                      toolchain.  
    8 00000000         ;*                      This module performs:
    9 00000000         ;*                      - Set the initial SP
   10 00000000         ;*                      - Set the initial PC == Reset_Ha
                       ndler
   11 00000000         ;*                      - Set the vector table entries w
                       ith the exceptions ISR address
   12 00000000         ;*                      - Branches to __main in the C li
                       brary (which eventually
   13 00000000         ;*                        calls main()).
   14 00000000         ;*                      After Reset the CortexM3 process
                       or is in Thread mode,
   15 00000000         ;*                      priority is Privileged, and the 
                       Stack is set to Main.
   16 00000000         ;* <<< Use Configuration Wizard in Context Menu >>>   
   17 00000000         ;*******************************************************
                       ************************
   18 00000000         ; THE PRESENT FIRMWARE WHICH IS FOR GUIDANCE ONLY AIMS A
                       T PROVIDING CUSTOMERS
   19 00000000         ; WITH CODING INFORMATION REGARDING THEIR PRODUCTS IN OR
                       DER FOR THEM TO SAVE TIME.
   20 00000000         ; AS A RESULT, STMICROELECTRONICS SHALL NOT BE HELD LIAB
                       LE FOR ANY DIRECT,
   21 00000000         ; INDIRECT OR CONSEQUENTIAL DAMAGES WITH RESPECT TO ANY 
                       CLAIMS ARISING FROM THE
   22 00000000         ; CONTENT OF SUCH FIRMWARE AND/OR THE USE MADE BY CUSTOM
                       ERS OF THE CODING
   23 00000000         ; INFORMATION CONTAINED HEREIN IN CONNECTION WITH THEIR 
                       PRODUCTS.
   24 00000000         ;*******************************************************
                       ************************
   25 00000000         
   26 00000000         ; Amount of memory (in bytes) allocated for Stack
   27 00000000         ; Tailor this value to your application needs
   28 00000000         ; <h> Stack Configuration
   29 00000000         ;   <o> Stack Size (in Bytes) <0x0-0xFFFFFFFF:8>
   30 00000000         ; </h>
   31 00000000         
   32 00000000 00000200 
                       Stack_Size
                               EQU              0x00000200
   33 00000000         
   34 00000000                 AREA             STACK, NOINIT, READWRITE, ALIGN
=3
   35 00000000         Stack_Mem
                               SPACE            Stack_Size
   36 00000200         __initial_sp
   37 00000200         
   38 00000200         
   39 00000200         ; <h> Heap Configuration
   40 00000200         ;   <o>  Heap Size (in Bytes) <0x0-0xFFFFFFFF:8>



ARM Macro Assembler    Page 2 


   41 00000200         ; </h>
   42 00000200         
   43 00000200 00000000 
                       Heap_Size
                               EQU              0x00000000
   44 00000200         
   45 00000200                 AREA             HEAP, NOINIT, READWRITE, ALIGN=
3
   46 00000000         __heap_base
   47 00000000         Heap_Mem
                               SPACE            Heap_Size
   48 00000000         __heap_limit
   49 00000000         
   50 00000000                 PRESERVE8
   51 00000000                 THUMB
   52 00000000         
   53 00000000         
   54 00000000         ; Vector Table Mapped to Address 0 at Reset
   55 00000000                 AREA             RESET, DATA, READONLY
   56 00000000                 EXPORT           __Vectors
   57 00000000                 EXPORT           __Vectors_End
   58 00000000                 EXPORT           __Vectors_Size
   59 00000000         
   60 00000000 00000000 
                       __Vectors
                               DCD              __initial_sp ; Top of Stack
   61 00000004 00000000        DCD              Reset_Handler ; Reset Handler
   62 00000008 00000000        DCD              NMI_Handler ; NMI Handler
   63 0000000C 00000000        DCD              HardFault_Handler ; Hard Fault 
                                                            Handler
   64 00000010 00000000        DCD              MemManage_Handler 
                                                            ; MPU Fault Handler
                                                            
   65 00000014 00000000        DCD              BusFault_Handler 
                                                            ; Bus Fault Handler
                                                            
   66 00000018 00000000        DCD              UsageFault_Handler ; Usage Faul
                                                            t Handler
   67 0000001C 00000000        DCD              0           ; Reserved
   68 00000020 00000000        DCD              0           ; Reserved
   69 00000024 00000000        DCD              0           ; Reserved
   70 00000028 00000000        DCD              0           ; Reserved
   71 0000002C 00000000        DCD              SVC_Handler ; SVCall Handler
   72 00000030 00000000        DCD              DebugMon_Handler ; Debug Monito
                                                            r Handler
   73 00000034 00000000        DCD              0           ; Reserved
   74 00000038 00000000        DCD              PendSV_Handler ; PendSV Handler
                                                            
   75 0000003C 00000000        DCD              SysTick_Handler 
                                                            ; SysTick Handler
   76 00000040         
   77 00000040         ; External Interrupts
   78 00000040 00000000        DCD              WWDG_IRQHandler 
                                                            ; Window Watchdog
   79 00000044 00000000        DCD              PVD_IRQHandler ; PVD through EX
                                                            TI Line detect
   80 00000048 00000000        DCD              TAMPER_IRQHandler ; Tamper
   81 0000004C 00000000        DCD              RTC_IRQHandler ; RTC
   82 00000050 00000000        DCD              FLASH_IRQHandler ; Flash



ARM Macro Assembler    Page 3 


   83 00000054 00000000        DCD              RCC_IRQHandler ; RCC
   84 00000058 00000000        DCD              EXTI0_IRQHandler ; EXTI Line 0
   85 0000005C 00000000        DCD              EXTI1_IRQHandler ; EXTI Line 1
   86 00000060 00000000        DCD              EXTI2_IRQHandler ; EXTI Line 2
   87 00000064 00000000        DCD              EXTI3_IRQHandler ; EXTI Line 3
   88 00000068 00000000        DCD              EXTI4_IRQHandler ; EXTI Line 4
   89 0000006C 00000000        DCD              DMA1_Channel1_IRQHandler 
                                                            ; DMA1 Channel 1
   90 00000070 00000000        DCD              DMA1_Channel2_IRQHandler 
                                                            ; DMA1 Channel 2
   91 00000074 00000000        DCD              DMA1_Channel3_IRQHandler 
                                                            ; DMA1 Channel 3
   92 00000078 00000000        DCD              DMA1_Channel4_IRQHandler 
                                                            ; DMA1 Channel 4
   93 0000007C 00000000        DCD              DMA1_Channel5_IRQHandler 
                                                            ; DMA1 Channel 5
   94 00000080 00000000        DCD              DMA1_Channel6_IRQHandler 
                                                            ; DMA1 Channel 6
   95 00000084 00000000        DCD              DMA1_Channel7_IRQHandler 
                                                            ; DMA1 Channel 7
   96 00000088 00000000        DCD              ADC1_2_IRQHandler ; ADC1_2
   97 0000008C 00000000        DCD              USB_HP_CAN1_TX_IRQHandler ; USB
                                                             High Priority or C
                                                            AN1 TX
   98 00000090 00000000        DCD              USB_LP_CAN1_RX0_IRQHandler ; US
                                                            B Low  Priority or 
                                                            CAN1 RX0
   99 00000094 00000000        DCD              CAN1_RX1_IRQHandler ; CAN1 RX1
  100 00000098 00000000        DCD              CAN1_SCE_IRQHandler ; CAN1 SCE
  101 0000009C 00000000        DCD              EXTI9_5_IRQHandler 
                                                            ; EXTI Line 9..5
  102 000000A0 00000000        DCD              TIM1_BRK_IRQHandler 
                                                            ; TIM1 Break
  103 000000A4 00000000        DCD              TIM1_UP_IRQHandler 
                                                            ; TIM1 Update
  104 000000A8 00000000        DCD              TIM1_TRG_COM_IRQHandler ; TIM1 
                                                            Trigger and Commuta
                                                            tion
  105 000000AC 00000000        DCD              TIM1_CC_IRQHandler ; TIM1 Captu
                                                            re Compare
  106 000000B0 00000000        DCD              TIM2_IRQHandler ; TIM2
  107 000000B4 00000000        DCD              TIM3_IRQHandler ; TIM3
  108 000000B8 00000000        DCD              TIM4_IRQHandler ; TIM4
  109 000000BC 00000000        DCD              I2C1_EV_IRQHandler ; I2C1 Event
                                                            
  110 000000C0 00000000        DCD              I2C1_ER_IRQHandler ; I2C1 Error
                                                            
  111 000000C4 00000000        DCD              I2C2_EV_IRQHandler ; I2C2 Event
                                                            
  112 000000C8 00000000        DCD              I2C2_ER_IRQHandler ; I2C2 Error
                                                            
  113 000000CC 00000000        DCD              SPI1_IRQHandler ; SPI1
  114 000000D0 00000000        DCD              SPI2_IRQHandler ; SPI2
  115 000000D4 00000000        DCD              USART1_IRQHandler ; USART1
  116 000000D8 00000000        DCD              USART2_IRQHandler ; USART2
  117 000000DC 00000000        DCD              USART3_IRQHandler ; USART3
  118 000000E0 00000000        DCD              EXTI15_10_IRQHandler 
                                                            ; EXTI Line 15..10
  119 000000E4 00000000        DCD              RTCAlarm_IRQHandler ; RTC Alarm



ARM Macro Assembler    Page 4 


                                                             through EXTI Line
  120 000000E8 00000000        DCD              USBWakeUp_IRQHandler ; USB Wake
                                                            up from suspend
  121 000000EC         __Vectors_End
  122 000000EC         
  123 000000EC 000000EC 
                       __Vectors_Size
                               EQU              __Vectors_End - __Vectors
  124 000000EC         
  125 000000EC                 AREA             |.text|, CODE, READONLY
  126 00000000         
  127 00000000         ; Reset handler routine
  128 00000000         Reset_Handler
                               PROC
  129 00000000                 EXPORT           Reset_Handler             [WEAK
]
  130 00000000                 IMPORT           __main
  131 00000000 4805            LDR              R0, =__main
  132 00000002 4700            BX               R0
  133 00000004                 ENDP
  134 00000004         
  135 00000004         ; Dummy Exception Handlers (infinite loops which can be 
                       modified)
  136 00000004         
  137 00000004         NMI_Handler
                               PROC
  138 00000004                 EXPORT           NMI_Handler                [WEA
K]
  139 00000004 E7FE            B                .
  140 00000006                 ENDP
  142 00000006         HardFault_Handler
                               PROC
  143 00000006                 EXPORT           HardFault_Handler          [WEA
K]
  144 00000006 E7FE            B                .
  145 00000008                 ENDP
  147 00000008         MemManage_Handler
                               PROC
  148 00000008                 EXPORT           MemManage_Handler          [WEA
K]
  149 00000008 E7FE            B                .
  150 0000000A                 ENDP
  152 0000000A         BusFault_Handler
                               PROC
  153 0000000A                 EXPORT           BusFault_Handler           [WEA
K]
  154 0000000A E7FE            B                .
  155 0000000C                 ENDP
  157 0000000C         UsageFault_Handler
                               PROC
  158 0000000C                 EXPORT           UsageFault_Handler         [WEA
K]
  159 0000000C E7FE            B                .
  160 0000000E                 ENDP
  161 0000000E         SVC_Handler
                               PROC
  162 0000000E                 EXPORT           SVC_Handler                [WEA
K]
  163 0000000E E7FE            B                .



ARM Macro Assembler    Page 5 


  164 00000010                 ENDP
  166 00000010         DebugMon_Handler
                               PROC
  167 00000010                 EXPORT           DebugMon_Handler           [WEA
K]
  168 00000010 E7FE            B                .
  169 00000012                 ENDP
  170 00000012         PendSV_Handler
                               PROC
  171 00000012                 EXPORT           PendSV_Handler             [WEA
K]
  172 00000012 E7FE            B                .
  173 00000014                 ENDP
  174 00000014         SysTick_Handler
                               PROC
  175 00000014                 EXPORT           SysTick_Handler            [WEA
K]
  176 00000014 E7FE            B                .
  177 00000016                 ENDP
  178 00000016         
  179 00000016         Default_Handler
                               PROC
  180 00000016         
  181 00000016                 EXPORT           WWDG_IRQHandler            [WEA
K]
  182 00000016                 EXPORT           PVD_IRQHandler             [WEA
K]
  183 00000016                 EXPORT           TAMPER_IRQHandler          [WEA
K]
  184 00000016                 EXPORT           RTC_IRQHandler             [WEA
K]
  185 00000016                 EXPORT           FLASH_IRQHandler           [WEA
K]
  186 00000016                 EXPORT           RCC_IRQHandler             [WEA
K]
  187 00000016                 EXPORT           EXTI0_IRQHandler           [WEA
K]
  188 00000016                 EXPORT           EXTI1_IRQHandler           [WEA
K]
  189 00000016                 EXPORT           EXTI2_IRQHandler           [WEA
K]
  190 00000016                 EXPORT           EXTI3_IRQHandler           [WEA
K]
  191 00000016                 EXPORT           EXTI4_IRQHandler           [WEA
K]
  192 00000016                 EXPORT           DMA1_Channel1_IRQHandler   [WEA
K]
  193 00000016                 EXPORT           DMA1_Channel2_IRQHandler   [WEA
K]
  194 00000016                 EXPORT           DMA1_Channel3_IRQHandler   [WEA
K]
  195 00000016                 EXPORT           DMA1_Channel4_IRQHandler   [WEA
K]
  196 00000016                 EXPORT           DMA1_Channel5_IRQHandler   [WEA
K]
  197 00000016                 EXPORT           DMA1_Channel6_IRQHandler   [WEA
K]
  198 00000016                 EXPORT           DMA1_Channel7_IRQHandler   [WEA
K]



ARM Macro Assembler    Page 6 


  199 00000016                 EXPORT           ADC1_2_IRQHandler          [WEA
K]
  200 00000016                 EXPORT           USB_HP_CAN1_TX_IRQHandler  [WEA
K]
  201 00000016                 EXPORT           USB_LP_CAN1_RX0_IRQHandler [WEA
K]
  202 00000016                 EXPORT           CAN1_RX1_IRQHandler        [WEA
K]
  203 00000016                 EXPORT           CAN1_SCE_IRQHandler        [WEA
K]
  204 00000016                 EXPORT           EXTI9_5_IRQHandler         [WEA
K]
  205 00000016                 EXPORT           TIM1_BRK_IRQHandler        [WEA
K]
  206 00000016                 EXPORT           TIM1_UP_IRQHandler         [WEA
K]
  207 00000016                 EXPORT           TIM1_TRG_COM_IRQHandler    [WEA
K]
  208 00000016                 EXPORT           TIM1_CC_IRQHandler         [WEA
K]
  209 00000016                 EXPORT           TIM2_IRQHandler            [WEA
K]
  210 00000016                 EXPORT           TIM3_IRQHandler            [WEA
K]
  211 00000016                 EXPORT           TIM4_IRQHandler            [WEA
K]
  212 00000016                 EXPORT           I2C1_EV_IRQHandler         [WEA
K]
  213 00000016                 EXPORT           I2C1_ER_IRQHandler         [WEA
K]
  214 00000016                 EXPORT           I2C2_EV_IRQHandler         [WEA
K]
  215 00000016                 EXPORT           I2C2_ER_IRQHandler         [WEA
K]
  216 00000016                 EXPORT           SPI1_IRQHandler            [WEA
K]
  217 00000016                 EXPORT           SPI2_IRQHandler            [WEA
K]
  218 00000016                 EXPORT           USART1_IRQHandler          [WEA
K]
  219 00000016                 EXPORT           USART2_IRQHandler          [WEA
K]
  220 00000016                 EXPORT           USART3_IRQHandler          [WEA
K]
  221 00000016                 EXPORT           EXTI15_10_IRQHandler       [WEA
K]
  222 00000016                 EXPORT           RTCAlarm_IRQHandler        [WEA
K]
  223 00000016                 EXPORT           USBWakeUp_IRQHandler       [WEA
K]
  224 00000016         
  225 00000016         WWDG_IRQHandler
  226 00000016         PVD_IRQHandler
  227 00000016         TAMPER_IRQHandler
  228 00000016         RTC_IRQHandler
  229 00000016         FLASH_IRQHandler
  230 00000016         RCC_IRQHandler
  231 00000016         EXTI0_IRQHandler
  232 00000016         EXTI1_IRQHandler



ARM Macro Assembler    Page 7 


  233 00000016         EXTI2_IRQHandler
  234 00000016         EXTI3_IRQHandler
  235 00000016         EXTI4_IRQHandler
  236 00000016         DMA1_Channel1_IRQHandler
  237 00000016         DMA1_Channel2_IRQHandler
  238 00000016         DMA1_Channel3_IRQHandler
  239 00000016         DMA1_Channel4_IRQHandler
  240 00000016         DMA1_Channel5_IRQHandler
  241 00000016         DMA1_Channel6_IRQHandler
  242 00000016         DMA1_Channel7_IRQHandler
  243 00000016         ADC1_2_IRQHandler
  244 00000016         USB_HP_CAN1_TX_IRQHandler
  245 00000016         USB_LP_CAN1_RX0_IRQHandler
  246 00000016         CAN1_RX1_IRQHandler
  247 00000016         CAN1_SCE_IRQHandler
  248 00000016         EXTI9_5_IRQHandler
  249 00000016         TIM1_BRK_IRQHandler
  250 00000016         TIM1_UP_IRQHandler
  251 00000016         TIM1_TRG_COM_IRQHandler
  252 00000016         TIM1_CC_IRQHandler
  253 00000016         TIM2_IRQHandler
  254 00000016         TIM3_IRQHandler
  255 00000016         TIM4_IRQHandler
  256 00000016         I2C1_EV_IRQHandler
  257 00000016         I2C1_ER_IRQHandler
  258 00000016         I2C2_EV_IRQHandler
  259 00000016         I2C2_ER_IRQHandler
  260 00000016         SPI1_IRQHandler
  261 00000016         SPI2_IRQHandler
  262 00000016         USART1_IRQHandler
  263 00000016         USART2_IRQHandler
  264 00000016         USART3_IRQHandler
  265 00000016         EXTI15_10_IRQHandler
  266 00000016         RTCAlarm_IRQHandler
  267 00000016         USBWakeUp_IRQHandler
  268 00000016         
  269 00000016 E7FE            B                .
  270 00000018         
  271 00000018                 ENDP
  272 00000018         
  273 00000018                 ALIGN
  274 00000018         
  275 00000018         ;*******************************************************
                       ************************
  276 00000018         ; User Stack and Heap initialization
  277 00000018         ;*******************************************************
                       ************************
  278 00000018                 IF               :DEF:__MICROLIB
  279 00000018         
  280 00000018                 EXPORT           __initial_sp
  281 00000018                 EXPORT           __heap_base
  282 00000018                 EXPORT           __heap_limit
  283 00000018         
  284 00000018                 ELSE
  299                          ENDIF
  300 00000018         
  301 00000018                 END
              00000000 
Command Line: --debug --xref --diag_suppress=9931 --cpu=Cortex-M3 --apcs=interw



ARM Macro Assembler    Page 8 


ork --depend=.\objects\startup_stm32f10x_md.d -o.\objects\startup_stm32f10x_md.
o -IE:\keil\Keil\STM32F1xx_DFP\2.4.1\Device\Include --predefine="__MICROLIB SET
A 1" --predefine="__UVISION_VERSION SETA 541" --predefine="STM32F10X_MD SETA 1"
 --list=.\listings\startup_stm32f10x_md.lst ..\CORE\startup_stm32f10x_md.s



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

STACK 00000000

Symbol: STACK
   Definitions
      At line 34 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      None
Comment: STACK unused
Stack_Mem 00000000

Symbol: Stack_Mem
   Definitions
      At line 35 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      None
Comment: Stack_Mem unused
__initial_sp 00000200

Symbol: __initial_sp
   Definitions
      At line 36 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 60 in file ..\CORE\startup_stm32f10x_md.s
      At line 280 in file ..\CORE\startup_stm32f10x_md.s

3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

HEAP 00000000

Symbol: HEAP
   Definitions
      At line 45 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      None
Comment: HEAP unused
Heap_Mem 00000000

Symbol: Heap_Mem
   Definitions
      At line 47 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      None
Comment: Heap_Mem unused
__heap_base 00000000

Symbol: __heap_base
   Definitions
      At line 46 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 281 in file ..\CORE\startup_stm32f10x_md.s
Comment: __heap_base used once
__heap_limit 00000000

Symbol: __heap_limit
   Definitions
      At line 48 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 282 in file ..\CORE\startup_stm32f10x_md.s
Comment: __heap_limit used once
4 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

RESET 00000000

Symbol: RESET
   Definitions
      At line 55 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      None
Comment: RESET unused
__Vectors 00000000

Symbol: __Vectors
   Definitions
      At line 60 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 56 in file ..\CORE\startup_stm32f10x_md.s
      At line 123 in file ..\CORE\startup_stm32f10x_md.s

__Vectors_End 000000EC

Symbol: __Vectors_End
   Definitions
      At line 121 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 57 in file ..\CORE\startup_stm32f10x_md.s
      At line 123 in file ..\CORE\startup_stm32f10x_md.s

3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

.text 00000000

Symbol: .text
   Definitions
      At line 125 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      None
Comment: .text unused
ADC1_2_IRQHandler 00000016

Symbol: ADC1_2_IRQHandler
   Definitions
      At line 243 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 96 in file ..\CORE\startup_stm32f10x_md.s
      At line 199 in file ..\CORE\startup_stm32f10x_md.s

BusFault_Handler 0000000A

Symbol: BusFault_Handler
   Definitions
      At line 152 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 65 in file ..\CORE\startup_stm32f10x_md.s
      At line 153 in file ..\CORE\startup_stm32f10x_md.s

CAN1_RX1_IRQHandler 00000016

Symbol: CAN1_RX1_IRQHandler
   Definitions
      At line 246 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 99 in file ..\CORE\startup_stm32f10x_md.s
      At line 202 in file ..\CORE\startup_stm32f10x_md.s

CAN1_SCE_IRQHandler 00000016

Symbol: CAN1_SCE_IRQHandler
   Definitions
      At line 247 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 100 in file ..\CORE\startup_stm32f10x_md.s
      At line 203 in file ..\CORE\startup_stm32f10x_md.s

DMA1_Channel1_IRQHandler 00000016

Symbol: DMA1_Channel1_IRQHandler
   Definitions
      At line 236 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 89 in file ..\CORE\startup_stm32f10x_md.s
      At line 192 in file ..\CORE\startup_stm32f10x_md.s

DMA1_Channel2_IRQHandler 00000016

Symbol: DMA1_Channel2_IRQHandler
   Definitions
      At line 237 in file ..\CORE\startup_stm32f10x_md.s
   Uses



ARM Macro Assembler    Page 2 Alphabetic symbol ordering
Relocatable symbols

      At line 90 in file ..\CORE\startup_stm32f10x_md.s
      At line 193 in file ..\CORE\startup_stm32f10x_md.s

DMA1_Channel3_IRQHandler 00000016

Symbol: DMA1_Channel3_IRQHandler
   Definitions
      At line 238 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 91 in file ..\CORE\startup_stm32f10x_md.s
      At line 194 in file ..\CORE\startup_stm32f10x_md.s

DMA1_Channel4_IRQHandler 00000016

Symbol: DMA1_Channel4_IRQHandler
   Definitions
      At line 239 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 92 in file ..\CORE\startup_stm32f10x_md.s
      At line 195 in file ..\CORE\startup_stm32f10x_md.s

DMA1_Channel5_IRQHandler 00000016

Symbol: DMA1_Channel5_IRQHandler
   Definitions
      At line 240 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 93 in file ..\CORE\startup_stm32f10x_md.s
      At line 196 in file ..\CORE\startup_stm32f10x_md.s

DMA1_Channel6_IRQHandler 00000016

Symbol: DMA1_Channel6_IRQHandler
   Definitions
      At line 241 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 94 in file ..\CORE\startup_stm32f10x_md.s
      At line 197 in file ..\CORE\startup_stm32f10x_md.s

DMA1_Channel7_IRQHandler 00000016

Symbol: DMA1_Channel7_IRQHandler
   Definitions
      At line 242 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 95 in file ..\CORE\startup_stm32f10x_md.s
      At line 198 in file ..\CORE\startup_stm32f10x_md.s

DebugMon_Handler 00000010

Symbol: DebugMon_Handler
   Definitions
      At line 166 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 72 in file ..\CORE\startup_stm32f10x_md.s
      At line 167 in file ..\CORE\startup_stm32f10x_md.s

Default_Handler 00000016




ARM Macro Assembler    Page 3 Alphabetic symbol ordering
Relocatable symbols

Symbol: Default_Handler
   Definitions
      At line 179 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      None
Comment: Default_Handler unused
EXTI0_IRQHandler 00000016

Symbol: EXTI0_IRQHandler
   Definitions
      At line 231 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 84 in file ..\CORE\startup_stm32f10x_md.s
      At line 187 in file ..\CORE\startup_stm32f10x_md.s

EXTI15_10_IRQHandler 00000016

Symbol: EXTI15_10_IRQHandler
   Definitions
      At line 265 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 118 in file ..\CORE\startup_stm32f10x_md.s
      At line 221 in file ..\CORE\startup_stm32f10x_md.s

EXTI1_IRQHandler 00000016

Symbol: EXTI1_IRQHandler
   Definitions
      At line 232 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 85 in file ..\CORE\startup_stm32f10x_md.s
      At line 188 in file ..\CORE\startup_stm32f10x_md.s

EXTI2_IRQHandler 00000016

Symbol: EXTI2_IRQHandler
   Definitions
      At line 233 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 86 in file ..\CORE\startup_stm32f10x_md.s
      At line 189 in file ..\CORE\startup_stm32f10x_md.s

EXTI3_IRQHandler 00000016

Symbol: EXTI3_IRQHandler
   Definitions
      At line 234 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 87 in file ..\CORE\startup_stm32f10x_md.s
      At line 190 in file ..\CORE\startup_stm32f10x_md.s

EXTI4_IRQHandler 00000016

Symbol: EXTI4_IRQHandler
   Definitions
      At line 235 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 88 in file ..\CORE\startup_stm32f10x_md.s
      At line 191 in file ..\CORE\startup_stm32f10x_md.s



ARM Macro Assembler    Page 4 Alphabetic symbol ordering
Relocatable symbols


EXTI9_5_IRQHandler 00000016

Symbol: EXTI9_5_IRQHandler
   Definitions
      At line 248 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 101 in file ..\CORE\startup_stm32f10x_md.s
      At line 204 in file ..\CORE\startup_stm32f10x_md.s

FLASH_IRQHandler 00000016

Symbol: FLASH_IRQHandler
   Definitions
      At line 229 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 82 in file ..\CORE\startup_stm32f10x_md.s
      At line 185 in file ..\CORE\startup_stm32f10x_md.s

HardFault_Handler 00000006

Symbol: HardFault_Handler
   Definitions
      At line 142 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 63 in file ..\CORE\startup_stm32f10x_md.s
      At line 143 in file ..\CORE\startup_stm32f10x_md.s

I2C1_ER_IRQHandler 00000016

Symbol: I2C1_ER_IRQHandler
   Definitions
      At line 257 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 110 in file ..\CORE\startup_stm32f10x_md.s
      At line 213 in file ..\CORE\startup_stm32f10x_md.s

I2C1_EV_IRQHandler 00000016

Symbol: I2C1_EV_IRQHandler
   Definitions
      At line 256 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 109 in file ..\CORE\startup_stm32f10x_md.s
      At line 212 in file ..\CORE\startup_stm32f10x_md.s

I2C2_ER_IRQHandler 00000016

Symbol: I2C2_ER_IRQHandler
   Definitions
      At line 259 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 112 in file ..\CORE\startup_stm32f10x_md.s
      At line 215 in file ..\CORE\startup_stm32f10x_md.s

I2C2_EV_IRQHandler 00000016

Symbol: I2C2_EV_IRQHandler
   Definitions



ARM Macro Assembler    Page 5 Alphabetic symbol ordering
Relocatable symbols

      At line 258 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 111 in file ..\CORE\startup_stm32f10x_md.s
      At line 214 in file ..\CORE\startup_stm32f10x_md.s

MemManage_Handler 00000008

Symbol: MemManage_Handler
   Definitions
      At line 147 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 64 in file ..\CORE\startup_stm32f10x_md.s
      At line 148 in file ..\CORE\startup_stm32f10x_md.s

NMI_Handler 00000004

Symbol: NMI_Handler
   Definitions
      At line 137 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 62 in file ..\CORE\startup_stm32f10x_md.s
      At line 138 in file ..\CORE\startup_stm32f10x_md.s

PVD_IRQHandler 00000016

Symbol: PVD_IRQHandler
   Definitions
      At line 226 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 79 in file ..\CORE\startup_stm32f10x_md.s
      At line 182 in file ..\CORE\startup_stm32f10x_md.s

PendSV_Handler 00000012

Symbol: PendSV_Handler
   Definitions
      At line 170 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 74 in file ..\CORE\startup_stm32f10x_md.s
      At line 171 in file ..\CORE\startup_stm32f10x_md.s

RCC_IRQHandler 00000016

Symbol: RCC_IRQHandler
   Definitions
      At line 230 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 83 in file ..\CORE\startup_stm32f10x_md.s
      At line 186 in file ..\CORE\startup_stm32f10x_md.s

RTCAlarm_IRQHandler 00000016

Symbol: RTCAlarm_IRQHandler
   Definitions
      At line 266 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 119 in file ..\CORE\startup_stm32f10x_md.s
      At line 222 in file ..\CORE\startup_stm32f10x_md.s




ARM Macro Assembler    Page 6 Alphabetic symbol ordering
Relocatable symbols

RTC_IRQHandler 00000016

Symbol: RTC_IRQHandler
   Definitions
      At line 228 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 81 in file ..\CORE\startup_stm32f10x_md.s
      At line 184 in file ..\CORE\startup_stm32f10x_md.s

Reset_Handler 00000000

Symbol: Reset_Handler
   Definitions
      At line 128 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 61 in file ..\CORE\startup_stm32f10x_md.s
      At line 129 in file ..\CORE\startup_stm32f10x_md.s

SPI1_IRQHandler 00000016

Symbol: SPI1_IRQHandler
   Definitions
      At line 260 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 113 in file ..\CORE\startup_stm32f10x_md.s
      At line 216 in file ..\CORE\startup_stm32f10x_md.s

SPI2_IRQHandler 00000016

Symbol: SPI2_IRQHandler
   Definitions
      At line 261 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 114 in file ..\CORE\startup_stm32f10x_md.s
      At line 217 in file ..\CORE\startup_stm32f10x_md.s

SVC_Handler 0000000E

Symbol: SVC_Handler
   Definitions
      At line 161 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 71 in file ..\CORE\startup_stm32f10x_md.s
      At line 162 in file ..\CORE\startup_stm32f10x_md.s

SysTick_Handler 00000014

Symbol: SysTick_Handler
   Definitions
      At line 174 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 75 in file ..\CORE\startup_stm32f10x_md.s
      At line 175 in file ..\CORE\startup_stm32f10x_md.s

TAMPER_IRQHandler 00000016

Symbol: TAMPER_IRQHandler
   Definitions
      At line 227 in file ..\CORE\startup_stm32f10x_md.s



ARM Macro Assembler    Page 7 Alphabetic symbol ordering
Relocatable symbols

   Uses
      At line 80 in file ..\CORE\startup_stm32f10x_md.s
      At line 183 in file ..\CORE\startup_stm32f10x_md.s

TIM1_BRK_IRQHandler 00000016

Symbol: TIM1_BRK_IRQHandler
   Definitions
      At line 249 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 102 in file ..\CORE\startup_stm32f10x_md.s
      At line 205 in file ..\CORE\startup_stm32f10x_md.s

TIM1_CC_IRQHandler 00000016

Symbol: TIM1_CC_IRQHandler
   Definitions
      At line 252 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 105 in file ..\CORE\startup_stm32f10x_md.s
      At line 208 in file ..\CORE\startup_stm32f10x_md.s

TIM1_TRG_COM_IRQHandler 00000016

Symbol: TIM1_TRG_COM_IRQHandler
   Definitions
      At line 251 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 104 in file ..\CORE\startup_stm32f10x_md.s
      At line 207 in file ..\CORE\startup_stm32f10x_md.s

TIM1_UP_IRQHandler 00000016

Symbol: TIM1_UP_IRQHandler
   Definitions
      At line 250 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 103 in file ..\CORE\startup_stm32f10x_md.s
      At line 206 in file ..\CORE\startup_stm32f10x_md.s

TIM2_IRQHandler 00000016

Symbol: TIM2_IRQHandler
   Definitions
      At line 253 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 106 in file ..\CORE\startup_stm32f10x_md.s
      At line 209 in file ..\CORE\startup_stm32f10x_md.s

TIM3_IRQHandler 00000016

Symbol: TIM3_IRQHandler
   Definitions
      At line 254 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 107 in file ..\CORE\startup_stm32f10x_md.s
      At line 210 in file ..\CORE\startup_stm32f10x_md.s

TIM4_IRQHandler 00000016



ARM Macro Assembler    Page 8 Alphabetic symbol ordering
Relocatable symbols


Symbol: TIM4_IRQHandler
   Definitions
      At line 255 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 108 in file ..\CORE\startup_stm32f10x_md.s
      At line 211 in file ..\CORE\startup_stm32f10x_md.s

USART1_IRQHandler 00000016

Symbol: USART1_IRQHandler
   Definitions
      At line 262 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 115 in file ..\CORE\startup_stm32f10x_md.s
      At line 218 in file ..\CORE\startup_stm32f10x_md.s

USART2_IRQHandler 00000016

Symbol: USART2_IRQHandler
   Definitions
      At line 263 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 116 in file ..\CORE\startup_stm32f10x_md.s
      At line 219 in file ..\CORE\startup_stm32f10x_md.s

USART3_IRQHandler 00000016

Symbol: USART3_IRQHandler
   Definitions
      At line 264 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 117 in file ..\CORE\startup_stm32f10x_md.s
      At line 220 in file ..\CORE\startup_stm32f10x_md.s

USBWakeUp_IRQHandler 00000016

Symbol: USBWakeUp_IRQHandler
   Definitions
      At line 267 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 120 in file ..\CORE\startup_stm32f10x_md.s
      At line 223 in file ..\CORE\startup_stm32f10x_md.s

USB_HP_CAN1_TX_IRQHandler 00000016

Symbol: USB_HP_CAN1_TX_IRQHandler
   Definitions
      At line 244 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 97 in file ..\CORE\startup_stm32f10x_md.s
      At line 200 in file ..\CORE\startup_stm32f10x_md.s

USB_LP_CAN1_RX0_IRQHandler 00000016

Symbol: USB_LP_CAN1_RX0_IRQHandler
   Definitions
      At line 245 in file ..\CORE\startup_stm32f10x_md.s
   Uses



ARM Macro Assembler    Page 9 Alphabetic symbol ordering
Relocatable symbols

      At line 98 in file ..\CORE\startup_stm32f10x_md.s
      At line 201 in file ..\CORE\startup_stm32f10x_md.s

UsageFault_Handler 0000000C

Symbol: UsageFault_Handler
   Definitions
      At line 157 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 66 in file ..\CORE\startup_stm32f10x_md.s
      At line 158 in file ..\CORE\startup_stm32f10x_md.s

WWDG_IRQHandler 00000016

Symbol: WWDG_IRQHandler
   Definitions
      At line 225 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 78 in file ..\CORE\startup_stm32f10x_md.s
      At line 181 in file ..\CORE\startup_stm32f10x_md.s

55 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Absolute symbols

Heap_Size 00000000

Symbol: Heap_Size
   Definitions
      At line 43 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 47 in file ..\CORE\startup_stm32f10x_md.s
Comment: Heap_Size used once
Stack_Size 00000200

Symbol: Stack_Size
   Definitions
      At line 32 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 35 in file ..\CORE\startup_stm32f10x_md.s
Comment: Stack_Size used once
__Vectors_Size 000000EC

Symbol: __Vectors_Size
   Definitions
      At line 123 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 58 in file ..\CORE\startup_stm32f10x_md.s
Comment: __Vectors_Size used once
3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
External symbols

__main 00000000

Symbol: __main
   Definitions
      At line 130 in file ..\CORE\startup_stm32f10x_md.s
   Uses
      At line 131 in file ..\CORE\startup_stm32f10x_md.s
Comment: __main used once
1 symbol
404 symbols in table
