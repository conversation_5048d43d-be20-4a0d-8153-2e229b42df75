#include "card_manager.h"
#include "MFRC522.h" // MFRC522底层驱动函数接口

/*****************辰哥单片机设计******************
                                            STM32
 * 文件			:	卡片管理模块实现文件
 * 版本			: V1.0
 * 日期			: 2024.9.2
 * MCU			:	STM32F103C8T6
 * 接口			:	RFID卡片管理
 * BILIBILI	:	辰哥单片机设计
 * CSDN			:	辰哥单片机设计
 * 作者			:	辰哥

**********************BEGIN***********************/

/////////////////////////////////////////////////////////////////////
// 全局变量定义
/////////////////////////////////////////////////////////////////////

// 卡片数据库数组：存储所有授权卡片信息
ic_card_t card_arr[card_size] = {0};

// 卡片管理标志变量：用于控制卡片添加等操作
uint8_t MFRC522_flag = 0;

/////////////////////////////////////////////////////////////////////
// 卡片管理核心函数实现
/////////////////////////////////////////////////////////////////////

/**
 * @brief 在卡片数据库中查找指定卡片
 * @param card 要查找的卡片信息指针
 * @return 找到返回数组索引(0-63)，未找到返回-1
 */
int8_t CARD_find_card(ic_card_t *card)
{
    for (int i = 0; i < card_size; ++i)
    {
        if (memcmp(card->id, card_arr[i].id, 4) == 0 && memcmp(card->type, card_arr[i].type, 2) == 0)
        {
            return i; // 找到匹配的卡片，返回索引
        }
    }
    return -1; // 未找到匹配的卡片
}

/**
 * @brief 向卡片数据库添加新卡片
 * @param card 要添加的卡片信息指针
 * @return 成功返回数组索引(0-63)，失败返回-1(数据库已满)
 */
int8_t CARD_add_card(ic_card_t *card)
{
    int i = 0;
    // 查找第一个未使用的位置
    for (i = 0; i < card_size; ++i)
    {
        if (card_arr[i].used_flag == 0)
        {
            break; // 找到空闲位置
        }
    }

    if (i == card_size)
        return -1; // 数据库已满，无法添加

    // 清空目标位置并复制新卡片数据
    memset(card_arr[i].id, 0, 20);
    memset(card_arr[i].type, 0, 2);
    memcpy(card_arr[i].id, card->id, 20);
    memcpy(card_arr[i].type, card->type, 2);
    card_arr[i].used_flag = 1; // 标记为已使用
    return i;                  // 返回添加位置的索引
}

/**
 * @brief 字节数组转换为无符号32位整数
 * @param buffer 字节数组指针
 * @param size 数组大小
 * @return 转换后的32位无符号整数
 */
uint32_t CARD_uchar_to_uint(uint8_t *buffer, uint8_t size)
{
    uint32_t temp = 0;
    while (size--)
    {
        temp |= *buffer++;
        temp <<= 8;
    }
    return temp;
}

/**
 * @brief 卡片处理主函数：完整的卡片读取、验证、对比和串口输出流程
 * @note 这是应用层的核心函数，处理完整的RFID卡片识别流程
 */
void CARD_Proc(void)
{
    unsigned char status;           // 返回符
    static uint8_t error_count = 0; // 静态错误计数器，减少错误信息输出频率

    ic_card_t temp_card = {0};
    status = MFRC522_Request(PICC_REQALL, temp_card.type); // 寻卡
    if (status != MI_OK)
    {
        /*寻卡失败 - 每10次失败才输出一次错误信息*/
        error_count++;
        if (error_count >= 10)
        {
            printf("CARD_READ_ERROR: No card detected (checked %d times)\r\n", error_count);
            error_count = 0; // 重置计数器
        }

        MFRC522_Reset();
        MFRC522_AntennaOff();
        MFRC522_AntennaOn();
        return;
    }

    // 检测到卡片时重置错误计数器
    error_count = 0;

    status = MFRC522_Anticoll(temp_card.id); // 防冲突
    if (status != MI_OK)
    {
        /*防冲突失败 - 输出错误信息*/
        printf("CARD_READ_ERROR: Anti-collision failed\r\n");
        return;
    }

    int8_t num = CARD_find_card(&temp_card);
    /*读取卡片*/
    printf("Card search result: num=%d\r\n", num); // 输出关键变量num的值
    if (num != -1)                                 // 识别到卡片
    {
        /*卡片验证成功 - 输出详细信息*/
        printf("VERIFY_SUCCESS: Type=%02X%02X, ID=%02X%02X%02X%02X, Level=%d\r\n",
               temp_card.type[0], temp_card.type[1],
               temp_card.id[0], temp_card.id[1], temp_card.id[2], temp_card.id[3],
               card_arr[num].access_level);

        // 输出卡片名称信息
        printf("Card Name: %s\r\n", card_arr[num].name);

        // 输出验证状态
        printf("Status: CARD_VERIFY_SUCCESS\r\n");

        // 分隔线便于阅读
        printf("----------------------------------------\r\n");
    }
    else // 未保存此卡片
    {
        /*卡片验证失败 - 输出未授权卡片信息*/
        printf("VERIFY_FAILED: Unauthorized card, Type=%02X%02X, ID=%02X%02X%02X%02X\r\n",
               temp_card.type[0], temp_card.type[1],
               temp_card.id[0], temp_card.id[1], temp_card.id[2], temp_card.id[3]);

        // 输出验证状态
        printf("Status: CARD_VERIFY_FAILED\r\n");

        // 分隔线便于阅读
        printf("----------------------------------------\r\n");

        if (MFRC522_flag & MFRC522_FLAG_ADD)
        {
            MFRC522_flag &= ~MFRC522_FLAG_ADD;
            CARD_add_card(&temp_card);
            printf("New card added to database\r\n");
        }
    }

    MFRC522_Halt();
}

/**
 * @brief 卡片数据库初始化函数：预设授权卡片数据
 * @note 系统启动时调用，初始化管理员、用户、访客三张示例卡片
 */
void CARD_InitDatabase(void)
{
    // 清空整个数据库
    memset(card_arr, 0, sizeof(card_arr));

    // 添加管理员卡片 - 示例卡片1
    card_arr[0].id[0] = 0x12;
    card_arr[0].id[1] = 0x34;
    card_arr[0].id[2] = 0x56;
    card_arr[0].id[3] = 0x78;
    card_arr[0].type[0] = 0x04;
    card_arr[0].type[1] = 0x00;
    strcpy(card_arr[0].name, "Admin");
    card_arr[0].access_level = 9; // 最高权限
    card_arr[0].used_flag = 1;

    // 添加普通用户卡片 - 示例卡片2
    card_arr[1].id[0] = 0xAB;
    card_arr[1].id[1] = 0xCD;
    card_arr[1].id[2] = 0xEF;
    card_arr[1].id[3] = 0x01;
    card_arr[1].type[0] = 0x04;
    card_arr[1].type[1] = 0x00;
    strcpy(card_arr[1].name, "User01");
    card_arr[1].access_level = 5; // 中等权限
    card_arr[1].used_flag = 1;

    // 添加访客卡片 - 示例卡片3
    card_arr[2].id[0] = 0x11;
    card_arr[2].id[1] = 0x22;
    card_arr[2].id[2] = 0x33;
    card_arr[2].id[3] = 0x44;
    card_arr[2].type[0] = 0x04;
    card_arr[2].type[1] = 0x00;
    strcpy(card_arr[2].name, "Guest");
    card_arr[2].access_level = 1; // 最低权限
    card_arr[2].used_flag = 1;

    printf("Database initialized: 3 preset cards added\r\n");
    printf("Admin: 12345678, User: ABCDEF01, Guest: 11223344\r\n");
}
