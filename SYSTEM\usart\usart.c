#include "usart.h"
#include <stdarg.h>
#include <stdio.h>

/*****************杈板摜鍗曠墖鏈鸿�捐��******************
                                            STM32
 * 鏂囦欢			:	USART涓插彛閫氫俊妯″潡
 * 鐗堟湰			: V1.0
 * 鏃ユ湡			: 2024.9.2
 * MCU			:	STM32F103C8T6
 * 鎺ュ彛			:	USART1涓插彛閫氫俊鍜宲rintf閲嶅畾鍚�
 * BILIBILI	:	杈板摜鍗曠墖鏈鸿�捐��
 * CSDN			:	杈板摜鍗曠墖鏈鸿�捐��
 * 浣滆€�			:	杈板摜

**********************BEGIN***********************/

/**
 * @brief 整数转字符串函数：将整数转换为字符串
 * @param num 要转换的整数
 * @param str 存储结果的字符串缓冲区
 * @param base 进制（通常为10）
 * @return 转换后的字符串指针
 */
char *int_to_str(int num, char *str, int base)
{
    int i = 0;
    int isNegative = 0;

    // 处理0的情况
    if (num == 0)
    {
        str[i++] = '0';
        str[i] = '\0';
        return str;
    }

    // 处理负数
    if (num < 0 && base == 10)
    {
        isNegative = 1;
        num = -num;
    }

    // 逐位提取数字
    while (num != 0)
    {
        int rem = num % base;
        str[i++] = (rem > 9) ? (rem - 10) + 'a' : rem + '0';
        num = num / base;
    }

    // 添加负号
    if (isNegative)
        str[i++] = '-';

    str[i] = '\0'; // 字符串结束符

    // 反转字符串
    int start = 0;
    int end = i - 1;
    while (start < end)
    {
        char temp = str[start];
        str[start] = str[end];
        str[end] = temp;
        start++;
        end--;
    }

    return str;
}

/**
 * @brief USART1閰嶇疆鍑芥暟锛氬垵濮嬪寲USART1涓插彛閫氫俊
 * @note 閰嶇疆娉㈢壒鐜�115200锛�8浣嶆暟鎹�浣嶏紝1浣嶅仠姝�浣嶏紝鏃犳牎楠屼綅
 */
void USART1_Config(void)
{
    // 寮€鍚疷SART1鍜孏PIOA鏃堕挓
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_USART1 | RCC_APB2Periph_GPIOA, ENABLE);

    // 閰嶇疆USART1_TX (PA9)涓哄�嶇敤鎺ㄦ尳杈撳嚭
    GPIO_InitTypeDef GPIO_InitStructure;
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_9;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(GPIOA, &GPIO_InitStructure);

    // 閰嶇疆USART1_RX (PA10)涓烘诞绌鸿緭鍏�
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_10;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN_FLOATING;
    GPIO_Init(GPIOA, &GPIO_InitStructure);

    // 閰嶇疆USART1鍙傛暟
    USART_InitTypeDef USART_InitStructure;
    USART_InitStructure.USART_BaudRate = 115200;                                    // 娉㈢壒鐜�115200
    USART_InitStructure.USART_WordLength = USART_WordLength_8b;                     // 8浣嶆暟鎹�浣�
    USART_InitStructure.USART_StopBits = USART_StopBits_1;                          // 1浣嶅仠姝�浣�
    USART_InitStructure.USART_Parity = USART_Parity_No;                             // 鏃犳牎楠屼綅
    USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None; // 鏃犵‖浠舵祦鎺�
    USART_InitStructure.USART_Mode = USART_Mode_Tx | USART_Mode_Rx;                 // 鍙戦€佸拰鎺ユ敹妯″紡
    USART_Init(USART1, &USART_InitStructure);

    // 浣胯兘USART1
    USART_Cmd(USART1, ENABLE);
}

/**
 * @brief printf閲嶅畾鍚戝嚱鏁帮細灏唒rintf杈撳嚭閲嶅畾鍚戝埌USART1
 * @param ch 瑕佸彂閫佺殑瀛楃��
 * @param f 鏂囦欢鎸囬拡锛堟湭浣跨敤锛�
 * @return 鍙戦€佺殑瀛楃��
 */
int fputc(int ch, FILE *f)
{
    // 绛夊緟鍙戦€佹暟鎹�瀵勫瓨鍣ㄤ负绌�
    while (USART_GetFlagStatus(USART1, USART_FLAG_TXE) == RESET)
        ;

    // 鍙戦€佸瓧绗�
    USART_SendData(USART1, (uint8_t)ch);

    return ch;
}

/**
 * @brief USART1鏍煎紡鍖栬緭鍑哄嚱鏁帮細绫讳技printf鐨勬牸寮忓寲杈撳嚭
 * @param USARTx USART澶栬�炬寚閽�
 * @param Data 鏍煎紡鍖栧瓧绗︿覆
 * @param ... 鍙�鍙樺弬鏁�
 */
void USART1_printf(USART_TypeDef *USARTx, uint8_t *Data, ...)
{
    const char *s;
    int d;
    char buf[16];

    va_list ap;
    va_start(ap, Data);

    while (*Data != 0) // 鍒ゆ柇鏄�鍚﹀埌杈惧瓧绗︿覆缁撴潫绗�
    {
        if (*Data == 0x5c) // '\\'
        {
            switch (*++Data)
            {
            case 'r': // 鍥炶溅绗�
                USART_SendData(USARTx, 0x0d);
                Data++;
                break;
            case 'n': // 鎹㈣�岀��
                USART_SendData(USARTx, 0x0a);
                Data++;
                break;
            default:
                Data++;
                break;
            }
        }
        else if (*Data == '%')
        {
            switch (*++Data)
            {
            case 's': // 瀛楃�︿覆
                s = va_arg(ap, const char *);
                for (; *s; s++)
                {
                    USART_SendData(USARTx, *s);
                    while (USART_GetFlagStatus(USARTx, USART_FLAG_TXE) == RESET)
                        ;
                }
                Data++;
                break;
            case 'd': // 鍗佽繘鍒�
                d = va_arg(ap, int);
                int_to_str(d, buf, 10);
                for (s = buf; *s; s++)
                {
                    USART_SendData(USARTx, *s);
                    while (USART_GetFlagStatus(USARTx, USART_FLAG_TXE) == RESET)
                        ;
                }
                Data++;
                break;
            default:
                Data++;
                break;
            }
        }
        else
        {
            USART_SendData(USARTx, *Data++);
        }
        while (USART_GetFlagStatus(USARTx, USART_FLAG_TXE) == RESET)
            ;
    }
}
