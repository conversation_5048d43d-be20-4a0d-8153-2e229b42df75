#include "usart.h"
#include <stdarg.h>
#include <stdio.h>

/*****************辰哥单片机设计******************
                                            STM32
 * 文件			:	USART串口通信模块
 * 版本			: V1.0
 * 日期			: 2024.9.2
 * MCU			:	STM32F103C8T6
 * 接口			:	USART1串口通信和printf重定向
 * BILIBILI	:	辰哥单片机设计
 * CSDN			:	辰哥单片机设计
 * 作者			:	辰哥

**********************BEGIN***********************/

/**
 * @brief 整数转字符串函数：将整数转换为字符串
 * @param num 要转换的整数
 * @param str 存储结果的字符串缓冲区
 * @param base 进制（通常为10）
 * @return 转换后的字符串指针
 */
char *int_to_str(int num, char *str, int base)
{
    int i = 0;
    int isNegative = 0;

    // 处理0的情况
    if (num == 0)
    {
        str[i++] = '0';
        str[i] = '\0';
        return str;
    }

    // 处理负数
    if (num < 0 && base == 10)
    {
        isNegative = 1;
        num = -num;
    }

    // 逐位提取数字
    while (num != 0)
    {
        int rem = num % base;
        str[i++] = (rem > 9) ? (rem - 10) + 'a' : rem + '0';
        num = num / base;
    }

    // 添加负号
    if (isNegative)
        str[i++] = '-';

    str[i] = '\0'; // 字符串结束符

    // 反转字符串
    int start = 0;
    int end = i - 1;
    while (start < end)
    {
        char temp = str[start];
        str[start] = str[end];
        str[end] = temp;
        start++;
        end--;
    }

    return str;
}

/**
 * @brief USART1配置函数：初始化USART1串口通信
 * @note 配置波特率115200，8位数据位，1位停止位，无校验位
 */
void USART1_Config(void)
{
    // 开启USART1和GPIOA时钟
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_USART1 | RCC_APB2Periph_GPIOA, ENABLE);

    // 配置USART1_TX (PA9)为复用推挽输出
    GPIO_InitTypeDef GPIO_InitStructure;
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_9;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(GPIOA, &GPIO_InitStructure);

    // 配置USART1_RX (PA10)为浮空输入
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_10;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN_FLOATING;
    GPIO_Init(GPIOA, &GPIO_InitStructure);

    // 配置USART1参数
    USART_InitTypeDef USART_InitStructure;
    USART_InitStructure.USART_BaudRate = 115200;                                    // 波特率115200
    USART_InitStructure.USART_WordLength = USART_WordLength_8b;                     // 8位数据位
    USART_InitStructure.USART_StopBits = USART_StopBits_1;                          // 1位停止位
    USART_InitStructure.USART_Parity = USART_Parity_No;                             // 无校验位
    USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None; // 无硬件流控
    USART_InitStructure.USART_Mode = USART_Mode_Tx | USART_Mode_Rx;                 // 发送和接收模式
    USART_Init(USART1, &USART_InitStructure);

    // 使能USART1
    USART_Cmd(USART1, ENABLE);
}

/**
 * @brief printf重定向函数：将printf输出重定向到USART1
 * @param ch 要发送的字符
 * @param f 文件指针（未使用）
 * @return 发送的字符
 */
int fputc(int ch, FILE *f)
{
    // 等待发送数据寄存器为空
    while (USART_GetFlagStatus(USART1, USART_FLAG_TXE) == RESET)
        ;

    // 发送字符
    USART_SendData(USART1, (uint8_t)ch);

    return ch;
}

/**
 * @brief USART1格式化输出函数：类似printf的格式化输出
 * @param USARTx USART外设指针
 * @param Data 格式化字符串
 * @param ... 可变参数
 */
void USART1_printf(USART_TypeDef *USARTx, uint8_t *Data, ...)
{
    const char *s;
    int d;
    char buf[16];

    va_list ap;
    va_start(ap, Data);

    while (*Data != 0) // 判断是否到达字符串结束符
    {
        if (*Data == 0x5c) // '\\'
        {
            switch (*++Data)
            {
            case 'r': // 回车符
                USART_SendData(USARTx, 0x0d);
                Data++;
                break;
            case 'n': // 换行符
                USART_SendData(USARTx, 0x0a);
                Data++;
                break;
            default:
                Data++;
                break;
            }
        }
        else if (*Data == '%')
        {
            switch (*++Data)
            {
            case 's': // 字符串
                s = va_arg(ap, const char *);
                for (; *s; s++)
                {
                    USART_SendData(USARTx, *s);
                    while (USART_GetFlagStatus(USARTx, USART_FLAG_TXE) == RESET)
                        ;
                }
                Data++;
                break;
            case 'd': // 十进制
                d = va_arg(ap, int);
                int_to_str(d, buf, 10);
                for (s = buf; *s; s++)
                {
                    USART_SendData(USARTx, *s);
                    while (USART_GetFlagStatus(USARTx, USART_FLAG_TXE) == RESET)
                        ;
                }
                Data++;
                break;
            default:
                Data++;
                break;
            }
        }
        else
        {
            USART_SendData(USARTx, *Data++);
        }
        while (USART_GetFlagStatus(USARTx, USART_FLAG_TXE) == RESET)
            ;
    }
}
