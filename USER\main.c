#include "stm32f10x.h"
#include "led.h"
#include "usart.h"
#include "delay.h"
#include "oled.h"
#include "MFRC522.h"


/*****************辰哥单片机设计******************
											STM32
 * 项目			:	MFRC522射频模块实验                     
 * 版本			: V1.0
 * 日期			: 2024.9.2
 * MCU			:	STM32F103C8T6
 * 接口			:	参看MFRC522.h							
 * BILIBILI	:	辰哥单片机设计
 * CSDN			:	辰哥单片机设计
 * 作者			:	辰哥 

**********************BEGIN***********************/

unsigned char buf[20];  //存放卡序号

int main(void)
{ 
	unsigned char status;		//返回符
	unsigned int temp,i;
	
  SystemInit();//配置系统时钟为72M	
	delay_init(72);
	LED_Init();
	LED_On();
	USART1_Config();
	MFRC522_Init();
	printf ( "MFRC522 Test\r\n" );
	
	delay_ms(1000);

  while (1)
  {
		status = MFRC522_Request(PICC_REQALL, buf);//寻卡
			if (status != MI_OK)
			{    

					MFRC522_Reset();
					MFRC522_AntennaOff(); 
					MFRC522_AntennaOn(); 
					continue;
			}

			printf("卡的类型:");
			for(i=0;i<2;i++)
			{
					temp=buf[i];
					printf("%X",temp);

			}
		
			status = MFRC522_Anticoll(buf);//防冲突
			if (status != MI_OK)
			{    
						continue;    
			}
			
			////////以下为超级终端打印出的内容////////////////////////

			printf("卡序列号：");	//超级终端显示,
			for(i=0;i<4;i++)
			{
					temp=buf[i];
					printf("%X",temp);

			}
			
			printf("\r\n");

  }
}





