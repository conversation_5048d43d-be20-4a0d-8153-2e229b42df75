#include "stm32f10x.h"
#include "led.h"
#include "usart.h"
#include "delay.h"
#include "oled.h"
#include "MFRC522.h"

/*****************辰哥单片机设计******************
											STM32
 * 项目			:	MFRC522射频模块实验
 * 版本			: V1.0
 * 日期			: 2024.9.2
 * MCU			:	STM32F103C8T6
 * 接口			:	参看MFRC522.h
 * BILIBILI	:	辰哥单片机设计
 * CSDN			:	辰哥单片机设计
 * 作者			:	辰哥

**********************BEGIN***********************/

int main(void)
{

	SystemInit(); // 配置系统时钟为72M
	delay_init(72);
	LED_Init();
	LED_On();
	USART1_Config();
	MFRC522_Init();
	printf("MFRC522 Test\r\n");

	delay_ms(1000);

	while (1)
	{
		MFRC522_Proc(); // 调用完善的卡片处理函数
		delay_ms(100);	// 避免过于频繁的扫描，减少CPU占用
	}
}
