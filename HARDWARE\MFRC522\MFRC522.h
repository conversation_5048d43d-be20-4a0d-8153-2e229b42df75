#ifndef __MFRC522_H	   // 防止头文件重复包含的宏定义开始
#define __MFRC522_H	   // 定义头文件标识符
#include "stm32f10x.h" // 包含STM32F10x系列标准外设库头文件
#include "delay.h"	   // 包含延时函数头文件
#include "math.h"	   // 包含数学函数库头文件

/*****************辰哥单片机设计******************
											STM32
 * 文件			:	MFRC522射频模块h文件
 * 版本			: V1.0
 * 日期			: 2024.9.2
 * MCU			:	STM32F103C8T6
 * 接口			:	射频卡
 * BILIBILI	:	辰哥单片机设计
 * CSDN			:	辰哥单片机设计
 * 淘宝			:	辰哥

**********************BEGIN***********************/

/***************引脚定义及配置****************/
// MFRC522射频模块 GPIO引脚定义

/*********************************** RC522 引脚定义 *********************************************/
// SDA引脚定义 - SPI片选信号，用于选择MFRC522芯片进行通信
#define MFRC522_GPIO_SDA_PORT GPIOA
#define MFRC522_GPIO_SDA_PIN GPIO_Pin_4

// SCK引脚定义 - SPI时钟信号，用于同步数据传输
#define MFRC522_GPIO_SCK_PORT GPIOA
#define MFRC522_GPIO_SCK_PIN GPIO_Pin_5

// MOSI引脚定义 - SPI主机输出从机输入，MCU向MFRC522发送数据
#define MFRC522_GPIO_MOSI_PORT GPIOA
#define MFRC522_GPIO_MOSI_PIN GPIO_Pin_7

// MISO引脚定义 - SPI主机输入从机输出，MFRC522向MCU发送数据
#define MFRC522_GPIO_MISO_PORT GPIOA
#define MFRC522_GPIO_MISO_PIN GPIO_Pin_6

// RST引脚定义 - 复位信号，用于硬件复位MFRC522芯片
#define MFRC522_GPIO_RST_PORT GPIOB
#define MFRC522_GPIO_RST_PIN GPIO_Pin_0

/*********************END**********************/

// SDA引脚控制宏定义
#define MFRC522_SDA_L GPIO_ResetBits(MFRC522_GPIO_SDA_PORT, MFRC522_GPIO_SDA_PIN) // SDA拉低，选中芯片
#define MFRC522_SDA_H GPIO_SetBits(MFRC522_GPIO_SDA_PORT, MFRC522_GPIO_SDA_PIN)	  // SDA拉高，取消选中

// RST引脚控制宏定义
#define MFRC522_RST_L GPIO_ResetBits(MFRC522_GPIO_RST_PORT, MFRC522_GPIO_RST_PIN) // RST拉低，复位芯片
#define MFRC522_RST_H GPIO_SetBits(MFRC522_GPIO_RST_PORT, MFRC522_GPIO_RST_PIN)	  // RST拉高，正常工作

// SCK引脚控制宏定义
#define MFRC522_SCK_L GPIO_ResetBits(MFRC522_GPIO_SCK_PORT, MFRC522_GPIO_SCK_PIN) // SCK拉低，时钟低电平
#define MFRC522_SCK_H GPIO_SetBits(MFRC522_GPIO_SCK_PORT, MFRC522_GPIO_SCK_PIN)	  // SCK拉高，时钟高电平

// MOSI引脚控制宏定义
#define MFRC522_MOSI_L GPIO_ResetBits(MFRC522_GPIO_MOSI_PORT, MFRC522_GPIO_MOSI_PIN) // MOSI拉低，发送0
#define MFRC522_MOSI_H GPIO_SetBits(MFRC522_GPIO_MOSI_PORT, MFRC522_GPIO_MOSI_PIN)	 // MOSI拉高，发送1

// MISO引脚读取宏定义
#define MFRC522_MISO_READ GPIO_ReadInputDataBit(MFRC522_GPIO_MISO_PORT, MFRC522_GPIO_MISO_PIN) // 读取MISO引脚状态

// MFRC522模块初始化函数声明
void MFRC522_Init(void);

/////////////////////////////////////////////////////////////////////
// MF522命令字 - MFRC522芯片内部命令字
/////////////////////////////////////////////////////////////////////
#define PCD_IDLE 0x00		// 空闲命令：取消当前命令执行的命令
#define PCD_AUTHENT 0x0E	// 验证命令：验证MIFARE卡的密钥
#define PCD_RECEIVE 0x08	// 接收命令：激活接收电路
#define PCD_TRANSMIT 0x04	// 发送命令：激活发送电路
#define PCD_TRANSCEIVE 0x0C // 收发命令：发送和接收电路
#define PCD_RESETPHASE 0x0F // 复位命令：复位MFRC522芯片

#define PCD_CALCCRC 0x03 // CRC计算命令：启动CRC协处理器

/////////////////////////////////////////////////////////////////////
// Mifare_One卡片命令字 - 与MIFARE卡通信的标准命令
/////////////////////////////////////////////////////////////////////
#define PICC_REQIDL 0x26	// 寻卡命令：寻找天线范围内处于空闲状态的卡片
#define PICC_REQALL 0x52	// 寻所有卡命令：寻找天线范围内的所有卡片（包括休眠状态）
#define PICC_ANTICOLL1 0x93 // 防冲撞命令1：当多张卡同时在场时进行防冲撞处理
#define PICC_ANTICOLL2 0x95 // 防冲撞命令2：用于处理双重大小的UID
#define PICC_AUTHENT1A 0x60 // 密钥A验证：使用密钥A对指定扇区进行验证
#define PICC_AUTHENT1B 0x61 // 密钥B验证：使用密钥B对指定扇区进行验证
#define PICC_READ 0x30		// 读命令：从指定地址读取16字节数据
#define PICC_WRITE 0xA0		// 写命令：向指定地址写入16字节数据
#define PICC_DECREMENT 0xC0 // 减值命令：对值块进行减法运算（电子钱包扣款）
#define PICC_INCREMENT 0xC1 // 增值命令：对值块进行加法运算（电子钱包充值）
#define PICC_RESTORE 0xC2	// 恢复命令：将指定块数据读取到内部数据寄存器
#define PICC_TRANSFER 0xB0	// 传输命令：将内部数据寄存器内容写入指定块
#define PICC_HALT 0x50		// 休眠命令：使卡片进入休眠状态

/////////////////////////////////////////////////////////////////////
// MF522 FIFO长度定义 - 内部缓冲区大小限制
/////////////////////////////////////////////////////////////////////
#define DEF_FIFO_LENGTH 64 // FIFO缓冲区大小：64字节
#define MAXRLEN 18		   // 最大接收长度：18字节

/////////////////////////////////////////////////////////////////////
// MF522寄存器地址 - MFRC522芯片内部寄存器地址映射
/////////////////////////////////////////////////////////////////////
// PAGE 0 - 命令和状态寄存器页
#define RFU00 0x00		   // 保留寄存器
#define CommandReg 0x01	   // 命令寄存器：启动和停止命令执行
#define ComIEnReg 0x02	   // 通信中断使能寄存器：控制中断请求的寄存器
#define DivlEnReg 0x03	   // 分频器中断使能寄存器：控制分频器中断
#define ComIrqReg 0x04	   // 通信中断请求寄存器：指示中断请求状态
#define DivIrqReg 0x05	   // 分频器中断请求寄存器：指示分频器中断状态
#define ErrorReg 0x06	   // 错误寄存器：显示上次命令执行的错误状态
#define Status1Reg 0x07	   // 状态寄存器1：包含通信状态标志
#define Status2Reg 0x08	   // 状态寄存器2：包含接收器和发送器状态
#define FIFODataReg 0x09   // FIFO数据寄存器：FIFO缓冲区的输入和输出
#define FIFOLevelReg 0x0A  // FIFO级别寄存器：指示FIFO缓冲区中存储的字节数
#define WaterLevelReg 0x0B // 水位寄存器：定义FIFO缓冲区溢出和下溢的级别
#define ControlReg 0x0C	   // 控制寄存器：包含各种控制位
#define BitFramingReg 0x0D // 位帧寄存器：面向位的帧的调整
#define CollReg 0x0E	   // 冲突寄存器：RF接口上检测到的第一个位冲突的位位置
#define RFU0F 0x0F		   // 保留寄存器
// PAGE 1 - 通信寄存器页
#define RFU10 0x10			// 保留寄存器
#define ModeReg 0x11		// 模式寄存器：定义发送和接收的一般模式
#define TxModeReg 0x12		// 发送模式寄存器：定义发送过程的数据传输速率
#define RxModeReg 0x13		// 接收模式寄存器：定义接收过程的数据传输速率
#define TxControlReg 0x14	// 发送控制寄存器：控制天线驱动器引脚TX1和TX2的逻辑行为
#define TxAutoReg 0x15		// 发送自动寄存器：控制天线驱动器的设置
#define TxSelReg 0x16		// 发送选择寄存器：选择天线驱动器的内部源
#define RxSelReg 0x17		// 接收选择寄存器：选择内部接收器设置
#define RxThresholdReg 0x18 // 接收阈值寄存器：选择位解码器的阈值
#define DemodReg 0x19		// 解调寄存器：定义解调器设置
#define RFU1A 0x1A			// 保留寄存器
#define RFU1B 0x1B			// 保留寄存器
#define MifareReg 0x1C		// MIFARE寄存器：控制ISO14443/MIFARE模式中的一些MIFARE通信
#define RFU1D 0x1D			// 保留寄存器
#define RFU1E 0x1E			// 保留寄存器
#define SerialSpeedReg 0x1F // 串行速度寄存器：选择串行UART接口的速度
// PAGE 2 - 配置寄存器页
#define RFU20 0x20			   // 保留寄存器
#define CRCResultRegM 0x21	   // CRC结果寄存器高字节：显示CRC计算的实际MSB值
#define CRCResultRegL 0x22	   // CRC结果寄存器低字节：显示CRC计算的实际LSB值
#define RFU23 0x23			   // 保留寄存器
#define ModWidthReg 0x24	   // 调制宽度寄存器：控制调制宽度设置
#define RFU25 0x25			   // 保留寄存器
#define RFCfgReg 0x26		   // RF配置寄存器：配置接收器增益
#define GsNReg 0x27			   // 增益选择寄存器：选择天线驱动器引脚TX1和TX2的电导调制
#define CWGsCfgReg 0x28		   // 载波配置寄存器：定义载波期间天线驱动器的电导
#define ModGsCfgReg 0x29	   // 调制配置寄存器：定义调制期间天线驱动器的电导
#define TModeReg 0x2A		   // 定时器模式寄存器：定义定时器的设置
#define TPrescalerReg 0x2B	   // 定时器预分频寄存器：定义定时器的预分频值
#define TReloadRegH 0x2C	   // 定时器重载寄存器高字节：定义16位定时器重载值的高字节
#define TReloadRegL 0x2D	   // 定时器重载寄存器低字节：定义16位定时器重载值的低字节
#define TCounterValueRegH 0x2E // 定时器计数器值寄存器高字节：显示16位定时器的实际值的高字节
#define TCounterValueRegL 0x2F // 定时器计数器值寄存器低字节：显示16位定时器的实际值的低字节
// PAGE 3 - 测试寄存器页
#define RFU30 0x30			 // 保留寄存器
#define TestSel1Reg 0x31	 // 测试选择寄存器1：一般测试信号配置
#define TestSel2Reg 0x32	 // 测试选择寄存器2：一般测试信号配置和PRBS控制
#define TestPinEnReg 0x33	 // 测试引脚使能寄存器：使能引脚输出驱动器在内部测试总线上
#define TestPinValueReg 0x34 // 测试引脚值寄存器：定义测试引脚D1-D7的值
#define TestBusReg 0x35		 // 测试总线寄存器：显示内部测试总线的状态
#define AutoTestReg 0x36	 // 自动测试寄存器：控制数字自测
#define VersionReg 0x37		 // 版本寄存器：显示软件版本
#define AnalogTestReg 0x38	 // 模拟测试寄存器：控制引脚AUX1和AUX2
#define TestDAC1Reg 0x39	 // 测试DAC寄存器1：定义TestDAC1的测试值
#define TestDAC2Reg 0x3A	 // 测试DAC寄存器2：定义TestDAC2的测试值
#define TestADCReg 0x3B		 // 测试ADC寄存器：显示ADC I和Q通道的值
#define RFU3C 0x3C			 // 保留寄存器
#define RFU3D 0x3D			 // 保留寄存器
#define RFU3E 0x3E			 // 保留寄存器
#define RFU3F 0x3F			 // 保留寄存器

/////////////////////////////////////////////////////////////////////
// 和MF522通讯时返回的错误代码 - 操作结果状态码
/////////////////////////////////////////////////////////////////////
#define MI_OK 0x26		 // 操作成功码
#define MI_NOTAGERR 0xcc // 未检测到卡片错误
#define MI_ERR 0xbb		 // 通信错误码

/////////////////////////////////////////////////////////////////////
// 通用定义和函数声明 - MFRC522操作函数接口
/////////////////////////////////////////////////////////////////////

#define macDummy_Data 0x00 // 虚拟数据：用于SPI通信时的占位数据

// 基础操作函数声明
char MFRC522_Reset(void);										// 复位MFRC522芯片
void Write_MFRC522(unsigned char Address, unsigned char value); // 向MFRC522寄存器写入数据
unsigned char Read_MFRC522(unsigned char Address);				// 从MFRC522寄存器读取数据
void MFRC522_AntennaOn(void);									// 开启天线
void MFRC522_AntennaOff(void);									// 关闭天线

// 卡片操作函数声明
char MFRC522_Request(unsigned char req_code, unsigned char *pTagType);										   // 请求卡片：寻找天线范围内的卡片
char MFRC522_Anticoll(unsigned char *pSnr);																	   // 防冲撞：获取卡片的序列号
char MFRC522_SelectTag(unsigned char *pSnr);																   // 选择卡片：选中指定序列号的卡片
char MFRC522_AuthState(unsigned char auth_mode, unsigned char addr, unsigned char *pKey, unsigned char *pSnr); // 认证：验证卡片扇区的密钥
char MFRC522_Read(unsigned char addr, unsigned char *pData);												   // 读取：从指定地址读取数据
char MFRC522_Halt(void);																					   // 休眠：使卡片进入休眠状态
char MFRC522_Write(unsigned char addr, unsigned char *pData);												   // 写入：向指定地址写入数据

// 注意：应用层功能函数已迁移到card_manager模块中

#endif /* __MFRC522__H */ // 防止头文件重复包含的宏定义结束