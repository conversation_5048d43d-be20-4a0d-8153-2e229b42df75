#include "MFRC522.h"   // 包含MFRC522头文件
#include "stm32f10x.h" // 包含STM32F10x系列标准外设库头文件
#include "delay.h"     // 包含延时函数头文件
#include "math.h"      // 包含数学函数库头文件
#include "stdio.h"     // 包含标准输入输出库，支持printf函数

/*****************辰哥单片机设计******************
                                            STM32
 * 文件			:	MFRC522射频模块h文件
 * 版本			: V1.0
 * 日期			: 2024.9.2
 * MCU			:	STM32F103C8T6
 * 接口			:	见代码
 * BILIBILI	:	辰哥单片机设计
 * CSDN			:	辰哥单片机设计
 * 作者			:	辰哥

**********************BEGIN***********************/

/***************根据自己需求更改****************/
// MFRC522射频模块 GPIO宏定义

/*********************************** RC522 引脚定义 *********************************************/
// SDA引脚定义 - SPI片选信号，用于选择MFRC522芯片进行通信
#define MFRC522_GPIO_SDA_PORT GPIOA
#define MFRC522_GPIO_SDA_PIN GPIO_Pin_4

// SCK引脚定义 - SPI时钟信号，用于同步数据传输
#define MFRC522_GPIO_SCK_PORT GPIOA
#define MFRC522_GPIO_SCK_PIN GPIO_Pin_5

// MOSI引脚定义 - SPI主机输出从机输入，MCU向MFRC522发送数据
#define MFRC522_GPIO_MOSI_PORT GPIOA
#define MFRC522_GPIO_MOSI_PIN GPIO_Pin_7

// MISO引脚定义 - SPI主机输入从机输出，MFRC522向MCU发送数据
#define MFRC522_GPIO_MISO_PORT GPIOA
#define MFRC522_GPIO_MISO_PIN GPIO_Pin_6

// RST引脚定义 - 复位信号，用于硬件复位MFRC522芯片
#define MFRC522_GPIO_RST_PORT GPIOB
#define MFRC522_GPIO_RST_PIN GPIO_Pin_0

/*********************END**********************/

// SDA引脚控制宏定义
#define MFRC522_SDA_L GPIO_ResetBits(MFRC522_GPIO_SDA_PORT, MFRC522_GPIO_SDA_PIN) // SDA拉低，选中芯片
#define MFRC522_SDA_H GPIO_SetBits(MFRC522_GPIO_SDA_PORT, MFRC522_GPIO_SDA_PIN)   // SDA拉高，取消选中

// RST引脚控制宏定义
#define MFRC522_RST_L GPIO_ResetBits(MFRC522_GPIO_RST_PORT, MFRC522_GPIO_RST_PIN) // RST拉低，复位芯片
#define MFRC522_RST_H GPIO_SetBits(MFRC522_GPIO_RST_PORT, MFRC522_GPIO_RST_PIN)   // RST拉高，正常工作

// SCK引脚控制宏定义
#define MFRC522_SCK_L GPIO_ResetBits(MFRC522_GPIO_SCK_PORT, MFRC522_GPIO_SCK_PIN) // SCK拉低，时钟低电平
#define MFRC522_SCK_H GPIO_SetBits(MFRC522_GPIO_SCK_PORT, MFRC522_GPIO_SCK_PIN)   // SCK拉高，时钟高电平

// MOSI引脚控制宏定义
#define MFRC522_MOSI_L GPIO_ResetBits(MFRC522_GPIO_MOSI_PORT, MFRC522_GPIO_MOSI_PIN) // MOSI拉低，发送0
#define MFRC522_MOSI_H GPIO_SetBits(MFRC522_GPIO_MOSI_PORT, MFRC522_GPIO_MOSI_PIN)   // MOSI拉高，发送1

// MISO引脚读取宏定义
#define MFRC522_MISO_READ GPIO_ReadInputDataBit(MFRC522_GPIO_MISO_PORT, MFRC522_GPIO_MISO_PIN) // 读取MISO引脚状态

// MFRC522模块初始化函数实现将在下方定义

/////////////////////////////////////////////////////////////////////
// MF522命令字 - MFRC522芯片内部命令定义
/////////////////////////////////////////////////////////////////////
#define PCD_IDLE 0x00       // 空闲命令：取消当前正在执行的命令
#define PCD_AUTHENT 0x0E    // 认证命令：验证MIFARE卡的密钥
#define PCD_RECEIVE 0x08    // 接收命令：激活接收器电路
#define PCD_TRANSMIT 0x04   // 发送命令：激活发送器电路
#define PCD_TRANSCEIVE 0x0C // 收发命令：激活发送和接收电路
#define PCD_RESETPHASE 0x0F // 复位命令：复位MFRC522芯片

#define PCD_CALCCRC 0x03 // CRC计算命令：激活CRC协处理器

/////////////////////////////////////////////////////////////////////
// Mifare_One卡片命令字 - 与MIFARE卡通信的标准命令
/////////////////////////////////////////////////////////////////////
#define PICC_REQIDL 0x26    // 请求空闲卡：寻找天线区域内未进入休眠状态的卡片
#define PICC_REQALL 0x52    // 请求所有卡：寻找天线区域内的所有卡片（包括休眠状态）
#define PICC_ANTICOLL1 0x93 // 防冲撞级联1：当多张卡同时在场时进行防冲撞处理
#define PICC_ANTICOLL2 0x95 // 防冲撞级联2：用于处理双重大小的UID
#define PICC_AUTHENT1A 0x60 // 密钥A认证：使用密钥A对指定扇区进行认证
#define PICC_AUTHENT1B 0x61 // 密钥B认证：使用密钥B对指定扇区进行认证
#define PICC_READ 0x30      // 读块命令：从指定块地址读取16字节数据
#define PICC_WRITE 0xA0     // 写块命令：向指定块地址写入16字节数据
#define PICC_DECREMENT 0xC0 // 减值命令：对值块进行减法操作（电子钱包扣款）
#define PICC_INCREMENT 0xC1 // 增值命令：对值块进行加法操作（电子钱包充值）
#define PICC_RESTORE 0xC2   // 恢复命令：将指定块的数据读取到内部数据寄存器
#define PICC_TRANSFER 0xB0  // 传输命令：将内部数据寄存器的内容写入指定块
#define PICC_HALT 0x50      // 休眠命令：使卡片进入休眠状态

/////////////////////////////////////////////////////////////////////
// MF522 FIFO长度定义 - 内部缓冲区大小定义
/////////////////////////////////////////////////////////////////////
#define DEF_FIFO_LENGTH 64 // FIFO缓冲区大小：64字节
#define MAXRLEN 18         // 最大接收长度：18字节

/////////////////////////////////////////////////////////////////////
// MF522寄存器定义 - MFRC522芯片内部寄存器地址映射
/////////////////////////////////////////////////////////////////////
// PAGE 0 - 命令和状态寄存器页
#define RFU00 0x00         // 保留寄存器
#define CommandReg 0x01    // 命令寄存器：启动和停止命令执行
#define ComIEnReg 0x02     // 通信中断使能寄存器：控制中断请求的传递
#define DivlEnReg 0x03     // 分频器中断使能寄存器：控制分频器中断
#define ComIrqReg 0x04     // 通信中断请求寄存器：指示中断请求状态
#define DivIrqReg 0x05     // 分频器中断请求寄存器：指示分频器中断状态
#define ErrorReg 0x06      // 错误寄存器：显示上次命令执行的错误状态
#define Status1Reg 0x07    // 状态寄存器1：包含通信状态标志
#define Status2Reg 0x08    // 状态寄存器2：包含接收器和发送器状态
#define FIFODataReg 0x09   // FIFO数据寄存器：FIFO缓冲区的输入输出
#define FIFOLevelReg 0x0A  // FIFO级别寄存器：指示FIFO缓冲区中存储的字节数
#define WaterLevelReg 0x0B // 水位寄存器：定义FIFO下溢和上溢警告的级别
#define ControlReg 0x0C    // 控制寄存器：各种控制位
#define BitFramingReg 0x0D // 位帧寄存器：面向位的帧的调整
#define CollReg 0x0E       // 冲突寄存器：RF接口上检测到的第一个位冲突的位位置
#define RFU0F 0x0F         // 保留寄存器
// PAGE 1 - 通信寄存器页
#define RFU10 0x10          // 保留寄存器
#define ModeReg 0x11        // 模式寄存器：定义发送和接收的一般模式
#define TxModeReg 0x12      // 发送模式寄存器：定义发送过程的数据传输速率
#define RxModeReg 0x13      // 接收模式寄存器：定义接收过程的数据传输速率
#define TxControlReg 0x14   // 发送控制寄存器：控制天线驱动器引脚TX1和TX2的逻辑行为
#define TxAutoReg 0x15      // 发送自动寄存器：控制天线驱动器的设置
#define TxSelReg 0x16       // 发送选择寄存器：选择天线驱动器的内部源
#define RxSelReg 0x17       // 接收选择寄存器：选择内部接收器设置
#define RxThresholdReg 0x18 // 接收阈值寄存器：选择位解码器的阈值
#define DemodReg 0x19       // 解调寄存器：定义解调器设置
#define RFU1A 0x1A          // 保留寄存器
#define RFU1B 0x1B          // 保留寄存器
#define MifareReg 0x1C      // MIFARE寄存器：控制ISO14443/MIFARE模式中的一些MIFARE通信
#define RFU1D 0x1D          // 保留寄存器
#define RFU1E 0x1E          // 保留寄存器
#define SerialSpeedReg 0x1F // 串行速度寄存器：选择串行UART接口的速度
// PAGE 2 - 配置寄存器页
#define RFU20 0x20             // 保留寄存器
#define CRCResultRegM 0x21     // CRC结果寄存器高字节：显示CRC计算的实际MSB值
#define CRCResultRegL 0x22     // CRC结果寄存器低字节：显示CRC计算的实际LSB值
#define RFU23 0x23             // 保留寄存器
#define ModWidthReg 0x24       // 调制宽度寄存器：控制调制宽度设置
#define RFU25 0x25             // 保留寄存器
#define RFCfgReg 0x26          // RF配置寄存器：配置接收器增益
#define GsNReg 0x27            // 增益选择寄存器：选择天线驱动器引脚TX1和TX2的调制电导
#define CWGsCfgReg 0x28        // 载波配置寄存器：定义载波期间天线驱动器的电导
#define ModGsCfgReg 0x29       // 调制配置寄存器：定义调制期间天线驱动器的电导
#define TModeReg 0x2A          // 定时器模式寄存器：定义定时器的设置
#define TPrescalerReg 0x2B     // 定时器预分频寄存器：定义定时器的预分频值
#define TReloadRegH 0x2C       // 定时器重载寄存器高字节：定义16位定时器重载值的高字节
#define TReloadRegL 0x2D       // 定时器重载寄存器低字节：定义16位定时器重载值的低字节
#define TCounterValueRegH 0x2E // 定时器计数器值寄存器高字节：显示16位定时器的实际值的高字节
#define TCounterValueRegL 0x2F // 定时器计数器值寄存器低字节：显示16位定时器的实际值的低字节
// PAGE 3 - 测试寄存器页
#define RFU30 0x30           // 保留寄存器
#define TestSel1Reg 0x31     // 测试选择寄存器1：一般测试信号配置
#define TestSel2Reg 0x32     // 测试选择寄存器2：一般测试信号配置和PRBS控制
#define TestPinEnReg 0x33    // 测试引脚使能寄存器：使能引脚输出驱动器在测试总线上
#define TestPinValueReg 0x34 // 测试引脚值寄存器：定义测试总线D1-D7的值
#define TestBusReg 0x35      // 测试总线寄存器：显示内部测试总线的状态
#define AutoTestReg 0x36     // 自动测试寄存器：控制数字自测试
#define VersionReg 0x37      // 版本寄存器：显示软件版本
#define AnalogTestReg 0x38   // 模拟测试寄存器：控制引脚AUX1和AUX2
#define TestDAC1Reg 0x39     // 测试DAC寄存器1：定义TestDAC1的测试值
#define TestDAC2Reg 0x3A     // 测试DAC寄存器2：定义TestDAC2的测试值
#define TestADCReg 0x3B      // 测试ADC寄存器：显示ADC I和Q通道的值
#define RFU3C 0x3C           // 保留寄存器
#define RFU3D 0x3D           // 保留寄存器
#define RFU3E 0x3E           // 保留寄存器
#define RFU3F 0x3F           // 保留寄存器

/////////////////////////////////////////////////////////////////////
// 和MF522通讯时返回的错误代码 - 函数返回状态定义
/////////////////////////////////////////////////////////////////////
#define MI_OK 0x26       // 操作成功完成
#define MI_NOTAGERR 0xcc // 未检测到卡片错误
#define MI_ERR 0xbb      // 通用错误代码

/////////////////////////////////////////////////////////////////////
// 通用定义和函数声明 - MFRC522操作函数接口
/////////////////////////////////////////////////////////////////////

#define macDummy_Data 0x00 // 虚拟数据：用于SPI通信时的占位数据

// 函数实现开始

/////////////////////////////////////////////////////////////////////
// 功    能：MFRC522模块初始化
// 参数说明：无
// 返    回：无
/////////////////////////////////////////////////////////////////////
void MFRC522_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;

    printf("MFRC522_Init: Starting initialization...\r\n"); // 调试信息

    // 开启GPIOA和GPIOB时钟
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA | RCC_APB2Periph_GPIOB, ENABLE);

    // 配置SDA引脚为推挽输出
    GPIO_InitStructure.GPIO_Pin = MFRC522_GPIO_SDA_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(MFRC522_GPIO_SDA_PORT, &GPIO_InitStructure);

    // 配置SCK引脚为推挽输出
    GPIO_InitStructure.GPIO_Pin = MFRC522_GPIO_SCK_PIN;
    GPIO_Init(MFRC522_GPIO_SCK_PORT, &GPIO_InitStructure);

    // 配置MOSI引脚为推挽输出
    GPIO_InitStructure.GPIO_Pin = MFRC522_GPIO_MOSI_PIN;
    GPIO_Init(MFRC522_GPIO_MOSI_PORT, &GPIO_InitStructure);

    // 配置MISO引脚为浮空输入
    GPIO_InitStructure.GPIO_Pin = MFRC522_GPIO_MISO_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN_FLOATING;
    GPIO_Init(MFRC522_GPIO_MISO_PORT, &GPIO_InitStructure);

    // 配置RST引脚为推挽输出
    GPIO_InitStructure.GPIO_Pin = MFRC522_GPIO_RST_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;
    GPIO_Init(MFRC522_GPIO_RST_PORT, &GPIO_InitStructure);

    // 初始化引脚状态
    MFRC522_SDA_H;                                    // SDA拉高，取消选中
    MFRC522_SCK_L;                                    // SCK拉低，时钟低电平
    MFRC522_MOSI_L;                                   // MOSI拉低
    printf("MFRC522_Init: GPIO pins configured\r\n"); // 调试信息

    // 复位MFRC522芯片
    MFRC522_RST_L;                                        // RST拉低，复位芯片
    delay_us(1);                                          // 延时1微秒
    MFRC522_RST_H;                                        // RST拉高，正常工作
    delay_us(1);                                          // 延时1微秒
    printf("MFRC522_Init: Hardware reset completed\r\n"); // 调试信息

    // 复位MFRC522
    MFRC522_Reset();
    printf("MFRC522_Init: Software reset completed\r\n"); // 调试信息

    // 配置工作模式
    Write_MFRC522(ModeReg, 0x3D);   // 设置为ISO14443A模式，CRC初始值0x6363
    Write_MFRC522(TxModeReg, 0x00); // 设置发送模式：106kBaud
    Write_MFRC522(RxModeReg, 0x00); // 设置接收模式：106kBaud
    Write_MFRC522(TxAutoReg, 0x40); // 设置自动发送模式

    // 配置定时器（用于超时检测）
    Write_MFRC522(TModeReg, 0x8D);      // 定时器自动启动，预分频器从低4位开始
    Write_MFRC522(TPrescalerReg, 0x3E); // 定时器预分频器值
    Write_MFRC522(TReloadRegL, 30);     // 定时器重载值低字节
    Write_MFRC522(TReloadRegH, 0);      // 定时器重载值高字节

    printf("MFRC522_Init: Work mode and timer configured\r\n"); // 调试信息

    // 开启天线
    MFRC522_AntennaOn();
    printf("MFRC522_Init: Antenna enabled\r\n"); // 调试信息

    // 测试基础通信 - 读取版本寄存器
    unsigned char version = Read_MFRC522(VersionReg);
    printf("MFRC522_Init: Version register = 0x%02X\r\n", version); // 调试信息

    // 测试寄存器读写功能
    printf("MFRC522_Init: Testing register read/write...\r\n");

    // 读取几个关键寄存器
    unsigned char commandReg = Read_MFRC522(CommandReg);
    unsigned char comIEnReg = Read_MFRC522(ComIEnReg);
    unsigned char fifoLevelReg = Read_MFRC522(FIFOLevelReg);

    printf("MFRC522_Init: CommandReg=0x%02X, ComIEnReg=0x%02X, FIFOLevelReg=0x%02X\r\n",
           commandReg, comIEnReg, fifoLevelReg);

    // 测试写入和读取多个值
    Write_MFRC522(ComIEnReg, 0x80);
    unsigned char testRead1 = Read_MFRC522(ComIEnReg);
    Write_MFRC522(ComIEnReg, 0x7F);
    unsigned char testRead2 = Read_MFRC522(ComIEnReg);
    Write_MFRC522(ComIEnReg, 0x00);
    unsigned char testRead3 = Read_MFRC522(ComIEnReg);

    printf("MFRC522_Init: SPI Test - 0x80->0x%02X, 0x7F->0x%02X, 0x00->0x%02X\r\n",
           testRead1, testRead2, testRead3);

    // 检查SPI通信是否正常
    if (testRead1 == 0x80 && testRead2 == 0x7F && testRead3 == 0x00)
    {
        printf("MFRC522_Init: SPI communication test PASSED\r\n");
    }
    else
    {
        printf("MFRC522_Init: SPI communication test FAILED - check wiring\r\n");
    }

    printf("MFRC522_Init: Initialization completed successfully\r\n"); // 调试信息

    // 检查关键寄存器状态
    printf("MFRC522_Init: Checking register status...\r\n");
    unsigned char modeReg = Read_MFRC522(ModeReg);
    unsigned char txModeReg = Read_MFRC522(TxModeReg);
    unsigned char rxModeReg = Read_MFRC522(RxModeReg);
    unsigned char txControlReg = Read_MFRC522(TxControlReg);
    unsigned char rfCfgReg = Read_MFRC522(RFCfgReg);

    printf("MFRC522_Init: ModeReg=0x%02X, TxModeReg=0x%02X, RxModeReg=0x%02X\r\n",
           modeReg, txModeReg, rxModeReg);
    printf("MFRC522_Init: TxControlReg=0x%02X, RFCfgReg=0x%02X\r\n",
           txControlReg, rfCfgReg);

    // 立即进行一次卡片检测测试
    printf("MFRC522_Init: Testing card detection capability...\r\n");
    unsigned char test_type[2];
    char test_status = MFRC522_Request(PICC_REQALL, test_type);
    if (test_status == MI_OK)
    {
        printf("MFRC522_Init: Card detected during test! Type=%02X%02X\r\n", test_type[0], test_type[1]);
    }
    else
    {
        printf("MFRC522_Init: No card detected during test (normal if no card present)\r\n");
        printf("MFRC522_Init: Test status=0x%02X (0x26=OK, 0xBB=ERR, 0xCC=NO_TAG)\r\n", test_status);
    }

    printf("MFRC522_Init: Ready for card detection - place IC card near antenna\r\n"); // 使用提示
}

/////////////////////////////////////////////////////////////////////
// 功    能：复位MFRC522芯片
// 参数说明：无
// 返    回：成功返回MI_OK
/////////////////////////////////////////////////////////////////////
char MFRC522_Reset(void)
{
    Write_MFRC522(CommandReg, PCD_RESETPHASE); // 发送复位命令
    delay_us(1);                               // 延时1微秒

    Write_MFRC522(CommandReg, PCD_IDLE); // 设置为空闲状态
    Write_MFRC522(ModeReg, 0x3D);        // 设置模式寄存器
    Write_MFRC522(TReloadRegL, 30);      // 设置定时器重载值低字节
    Write_MFRC522(TReloadRegH, 0);       // 设置定时器重载值高字节
    Write_MFRC522(TModeReg, 0x8D);       // 设置定时器模式
    Write_MFRC522(TPrescalerReg, 0x3E);  // 设置定时器预分频器

    return MI_OK;
}

/////////////////////////////////////////////////////////////////////
// 功    能：向MFRC522寄存器写入数据
// 参数说明：Address[IN]:寄存器地址
//           value[IN]:写入的数据
/////////////////////////////////////////////////////////////////////
void Write_MFRC522(unsigned char Address, unsigned char value)
{
    unsigned char i;

    MFRC522_SCK_L; // SCK拉低
    MFRC522_SDA_L; // SDA拉低，选中芯片

    // 发送地址字节（写操作，最高位为0）
    unsigned char addr = (Address << 1) & 0x7E; // 构造写地址，确保最高位为0
    for (i = 0; i < 8; i++)
    {
        MFRC522_SCK_L; // SCK拉低
        if ((addr << i) & 0x80)
            MFRC522_MOSI_H; // 发送1
        else
            MFRC522_MOSI_L; // 发送0
        delay_us(1);        // 添加1微秒延时确保时序
        MFRC522_SCK_H;      // SCK拉高，上升沿锁存数据
        delay_us(1);        // 添加1微秒延时确保时序
    }

    // 发送数据字节
    for (i = 0; i < 8; i++)
    {
        MFRC522_SCK_L; // SCK拉低
        if ((value << i) & 0x80)
            MFRC522_MOSI_H; // 发送1
        else
            MFRC522_MOSI_L; // 发送0
        delay_us(1);        // 添加1微秒延时确保时序
        MFRC522_SCK_H;      // SCK拉高，上升沿锁存数据
        delay_us(1);        // 添加1微秒延时确保时序
    }

    MFRC522_SCK_L; // SCK拉低
    MFRC522_SDA_H; // SDA拉高，取消选中
}

/////////////////////////////////////////////////////////////////////
// 功    能：从MFRC522寄存器读取数据
// 参数说明：Address[IN]:寄存器地址
// 返    回：读取的数据
/////////////////////////////////////////////////////////////////////
unsigned char Read_MFRC522(unsigned char Address)
{
    unsigned char i, value = 0;

    MFRC522_SCK_L; // SCK拉低
    MFRC522_SDA_L; // SDA拉低，选中芯片

    // 发送地址字节（读操作，最高位为1）
    unsigned char addr = (Address << 1) | 0x80; // 构造读地址
    for (i = 0; i < 8; i++)
    {
        MFRC522_SCK_L; // SCK拉低
        if ((addr << i) & 0x80)
            MFRC522_MOSI_H; // 发送1
        else
            MFRC522_MOSI_L; // 发送0
        delay_us(1);        // 添加1微秒延时确保时序
        MFRC522_SCK_H;      // SCK拉高，上升沿锁存数据
        delay_us(1);        // 添加1微秒延时确保时序
    }

    // 读取数据字节
    for (i = 0; i < 8; i++)
    {
        MFRC522_SCK_L; // SCK拉低
        value <<= 1;   // 左移一位
        delay_us(1);   // 添加1微秒延时确保时序
        MFRC522_SCK_H; // SCK拉高
        delay_us(1);   // 添加1微秒延时确保时序
        if (MFRC522_MISO_READ)
            value |= 0x01; // 读取到1
    }

    MFRC522_SCK_L; // SCK拉低
    MFRC522_SDA_H; // SDA拉高，取消选中

    return value;
}

/////////////////////////////////////////////////////////////////////
// 功    能：置RC522寄存器位
// 参数说明：reg[IN]:寄存器地址
//           mask[IN]:置位值
/////////////////////////////////////////////////////////////////////
void SetBitMask(unsigned char reg, unsigned char mask)
{
    char tmp = 0x0;                 // 临时变量存储寄存器值
    tmp = Read_MFRC522(reg);        // 读取当前寄存器值
    Write_MFRC522(reg, tmp | mask); // 将指定位置1后写回寄存器
}

/////////////////////////////////////////////////////////////////////
// 功    能：清RC522寄存器位
// 参数说明：reg[IN]:寄存器地址
//           mask[IN]:清位值
/////////////////////////////////////////////////////////////////////
void ClearBitMask(unsigned char reg, unsigned char mask)
{
    char tmp = 0x0;                  // 临时变量存储寄存器值
    tmp = Read_MFRC522(reg);         // 读取当前寄存器值
    Write_MFRC522(reg, tmp & ~mask); // 将指定位清零后写回寄存器
}

/////////////////////////////////////////////////////////////////////
// 功    能：通过RC522和ISO14443卡通讯
// 参数说明：Command[IN]:RC522命令字
//           pInData[IN]:通过RC522发送到卡片的数据
//           InLenByte[IN]:发送数据的字节长度
//           pOutData[OUT]:接收到的卡片返回数据
//           *pOutLenBit[OUT]:返回数据的位长度
/////////////////////////////////////////////////////////////////////
char MFRC522_ToCard(unsigned char Command,
                    unsigned char *pInData,
                    unsigned char InLenByte,
                    unsigned char *pOutData,
                    unsigned int *pOutLenBit)
{
    char status = MI_ERR;         // 初始化状态为错误
    unsigned char irqEn = 0x00;   // 中断使能标志
    unsigned char waitFor = 0x00; // 等待的中断标志
    unsigned char lastBits;       // 最后接收的位数
    unsigned char n;              // 临时变量
    unsigned int i;               // 循环计数器

    // 根据命令类型设置相应的中断使能和等待标志
    switch (Command)
    {
    case PCD_AUTHENT:   // 认证命令
        irqEn = 0x12;   // 使能IdleIRq和ErrIRq中断
        waitFor = 0x10; // 等待IdleIRq中断
        break;
    case PCD_TRANSCEIVE: // 收发命令
        irqEn = 0x77;    // 使能TxIRq、RxIRq、IdleIRq、LoAlertIRq、ErrIRq、TimerIRq中断
        waitFor = 0x30;  // 等待RxIRq和IdleIRq中断
        break;
    default:
        break;
    }

    Write_MFRC522(ComIEnReg, irqEn | 0x80); // 设置中断使能寄存器，使能IRQ引脚输出
    ClearBitMask(ComIrqReg, 0x80);          // 清除Set1标志，设置IRQ为开漏输出
    Write_MFRC522(CommandReg, PCD_IDLE);    // 取消当前命令，进入空闲状态
    SetBitMask(FIFOLevelReg, 0x80);         // 清空FIFO缓冲区

    // 将发送数据写入FIFO缓冲区
    for (i = 0; i < InLenByte; i++)
    {
        Write_MFRC522(FIFODataReg, pInData[i]); // 逐字节写入FIFO
    }
    Write_MFRC522(CommandReg, Command); // 执行指定命令

    // 如果是收发命令，启动发送
    if (Command == PCD_TRANSCEIVE)
    {
        SetBitMask(BitFramingReg, 0x80); // 启动数据发送
    }

    n = Read_MFRC522(ComIrqReg); // 读取中断状态
    i = 3000;                    // 增加超时计数器（约50ms），给卡片更多响应时间
    // 等待命令执行完成或超时
    do
    {
        n = Read_MFRC522(ComIrqReg); // 读取中断状态寄存器

        i--; // 超时计数器递减

    } while ((i != 0) && !(n & 0x01) && !(n & waitFor)); // 等待超时或TimerIRq或指定中断发生
    ClearBitMask(BitFramingReg, 0x80); // 停止发送

    if (i != 0) // 如果没有超时
    {
        unsigned char errorReg = Read_MFRC522(ErrorReg); // 读取错误寄存器
        static uint8_t tocard_debug_count = 0;           // 静态调试计数器

        // 只在前几次或有错误时输出调试信息
        if (tocard_debug_count < 3 || errorReg != 0)
        {
            printf("MFRC522_ToCard: ErrorReg=0x%02X, ComIrqReg=0x%02X\r\n", errorReg, n); // 调试信息
        }
        tocard_debug_count++;

        if (!(errorReg & 0x1B)) // 检查错误寄存器（BufferOvfl、ColErr、CRCErr、ParityErr）
        {
            status = MI_OK;       // 设置状态为成功
            if (n & irqEn & 0x01) // 检查是否有TimerIRq中断
            {
                status = MI_NOTAGERR; // 设置为无卡错误
                // 只在前几次输出无卡片的调试信息
                if (tocard_debug_count <= 3)
                {
                    printf("MFRC522_ToCard: Timer interrupt - no card\r\n"); // 调试信息
                }
            }
            if (Command == PCD_TRANSCEIVE) // 如果是收发命令
            {
                n = Read_MFRC522(FIFOLevelReg);             // 读取FIFO中的数据长度
                lastBits = Read_MFRC522(ControlReg) & 0x07; // 读取最后接收的位数
                if (lastBits)                               // 如果最后一个字节不是完整的8位
                {
                    *pOutLenBit = (n - 1) * 8 + lastBits; // 计算总位数
                }
                else
                {
                    *pOutLenBit = n * 8; // 计算总位数
                }
                if (n == 0) // 如果没有接收到数据
                {
                    n = 1; // 至少读取1个字节
                }
                if (n > MAXRLEN) // 如果接收数据超过最大长度
                {
                    n = MAXRLEN; // 限制为最大长度
                }
                // 从FIFO读取接收到的数据
                for (i = 0; i < n; i++)
                {
                    pOutData[i] = Read_MFRC522(FIFODataReg); // 逐字节读取
                }
            }
        }
        else
        {
            status = MI_ERR;                                                               // 有错误发生，设置错误状态
            printf("MFRC522_ToCard: Communication error - ErrorReg=0x%02X\r\n", errorReg); // 调试信息
            // 详细错误分析
            if (errorReg & 0x01)
                printf("  - ProtocolErr: Protocol error\r\n");
            if (errorReg & 0x02)
                printf("  - ParityErr: Parity error\r\n");
            if (errorReg & 0x04)
                printf("  - CRCErr: CRC error\r\n");
            if (errorReg & 0x08)
                printf("  - CollErr: Collision error\r\n");
            if (errorReg & 0x10)
                printf("  - BufferOvfl: Buffer overflow\r\n");
        }
    }
    else
    {
        status = MI_ERR;                                           // 超时错误
        printf("MFRC522_ToCard: Timeout error - no response\r\n"); // 调试信息
    }

    SetBitMask(ControlReg, 0x80);        // 停止定时器
    Write_MFRC522(CommandReg, PCD_IDLE); // 设置为空闲状态
    return status;                       // 返回操作状态
}

// 开启天线
// 每次启动或关闭天线发射之间应至少有1ms的间隔
void MFRC522_AntennaOn(void)
{
    unsigned char i;                                                  // 临时变量
    i = Read_MFRC522(TxControlReg);                                   // 读取发送控制寄存器
    printf("MFRC522_AntennaOn: TxControlReg before = 0x%02X\r\n", i); // 调试信息

    if (!(i & 0x03)) // 检查Tx1RFEn和Tx2RFEn位是否为0
    {
        SetBitMask(TxControlReg, 0x03);                     // 设置Tx1RFEn和Tx2RFEn位，开启天线
        printf("MFRC522_AntennaOn: Antenna turned on\r\n"); // 调试信息
    }
    else
    {
        printf("MFRC522_AntennaOn: Antenna already on\r\n"); // 调试信息
    }

    // 验证天线状态
    i = Read_MFRC522(TxControlReg);
    printf("MFRC522_AntennaOn: TxControlReg after = 0x%02X\r\n", i); // 调试信息

    // 设置天线增益为最大
    Write_MFRC522(RFCfgReg, 0x70); // 设置接收增益为最大(48dB)
    unsigned char rfCfg = Read_MFRC522(RFCfgReg);
    printf("MFRC522_AntennaOn: RFCfgReg = 0x%02X (gain setting)\r\n", rfCfg); // 调试信息
}

// 关闭天线
void MFRC522_AntennaOff(void)
{
    ClearBitMask(TxControlReg, 0x03); // 清除Tx1RFEn和Tx2RFEn位，关闭天线
}

// 功能描述?用MF522计算CRC
// 输入参数?pIndata--要读数CRC的数据?len--数据长度?pOutData--计算的CRC结果
void CalulateCRC(unsigned char *pIndata, unsigned char len, unsigned char *pOutData)
{
    unsigned char i, n;                  // 循环计数器和临时变量
    ClearBitMask(DivIrqReg, 0x04);       // 清除CRCIRq中断标志
    Write_MFRC522(CommandReg, PCD_IDLE); // 取消当前命令
    SetBitMask(FIFOLevelReg, 0x80);      // 清空FIFO缓冲区

    // 将数据写入FIFO
    for (i = 0; i < len; i++)
    {
        Write_MFRC522(FIFODataReg, *(pIndata + i)); // 逐字节写入FIFO
    }
    Write_MFRC522(CommandReg, PCD_CALCCRC); // 启动CRC计算命令
    i = 0xFF;                               // 设置超时计数器

    // 等待CRC计算完成
    do
    {
        n = Read_MFRC522(DivIrqReg); // 读取分频器中断寄存器
        i--;                         // 超时计数器递减
    } while ((i != 0) && !(n & 0x04)); // 等待CRCIRq中断或超时

    pOutData[0] = Read_MFRC522(CRCResultRegL); // 读取CRC结果低字节
    pOutData[1] = Read_MFRC522(CRCResultRegM); // 读取CRC结果高字节
}

/////////////////////////////////////////////////////////////////////
// 功    能：命令卡片进入休眠状态
// 返    回: 成功返回MI_OK
/////////////////////////////////////////////////////////////////////
char MFRC522_Halt(void)
{

    unsigned int unLen;
    unsigned char ucComMF522Buf[MAXRLEN];
    char status;
    ucComMF522Buf[0] = PICC_HALT;
    ucComMF522Buf[1] = 0;
    CalulateCRC(ucComMF522Buf, 2, &ucComMF522Buf[2]);

    status = MFRC522_ToCard(PCD_TRANSCEIVE, ucComMF522Buf, 4, ucComMF522Buf, &unLen);

    return MI_OK;
}

/////////////////////////////////////////////////////////////////////
// 功    能：寻卡
// 参数说明: req_code[IN]:寻卡方式
//                 0x52 = 寻感应区内所有符合14443A标准的卡
//                 0x26 = 寻未进入休眠状态的卡
//           pTagType[OUT]：卡片类型代码
//                 0x4400 = Mifare_UltraLight
//                 0x0400 = Mifare_One(S50)
//                 0x0200 = Mifare_One(S70)
//                 0x0800 = Mifare_Pro(X)
//                 0x4403 = Mifare_DESFire
// 返    回: 成功返回MI_OK
/////////////////////////////////////////////////////////////////////
char MFRC522_Request(unsigned char req_code, unsigned char *pTagType)
{
    char status;
    unsigned int unLen;
    unsigned char ucComMF522Buf[MAXRLEN];
    static uint8_t debug_count = 0; // 静态调试计数器，减少调试信息输出

    // 只在前几次或每50次输出调试信息
    if (debug_count < 3 || debug_count % 50 == 0)
    {
        printf("MFRC522_Request: Starting card detection, req_code=0x%02X\r\n", req_code); // 调试信息
    }
    debug_count++;

    ClearBitMask(Status2Reg, 0x08);     // 清除MFCrypto1On位，关闭加密
    Write_MFRC522(BitFramingReg, 0x07); // 设置位帧格式：发送最后字节的7位
    SetBitMask(TxControlReg, 0x03);     // 确保天线开启

    // 额外配置：确保接收器设置正确
    Write_MFRC522(RxSelReg, 0x86); // 设置接收器增益和阈值
    Write_MFRC522(RFCfgReg, 0x7F); // 设置接收器增益为最大值

    ucComMF522Buf[0] = req_code;

    status = MFRC522_ToCard(PCD_TRANSCEIVE, ucComMF522Buf, 1, ucComMF522Buf, &unLen);

    // 只在前几次或检测到卡片时输出详细调试信息
    if (debug_count <= 3 || status == MI_OK)
    {
        printf("MFRC522_Request: ToCard result - status=0x%02X, unLen=%d\r\n", status, unLen); // 调试信息
    }

    if ((status == MI_OK) && (unLen == 0x10))
    {
        *pTagType = ucComMF522Buf[0];
        *(pTagType + 1) = ucComMF522Buf[1];
        printf("MFRC522_Request: Card detected, Type=%02X%02X\r\n", *pTagType, *(pTagType + 1)); // 调试信息
    }
    else
    {
        status = MI_ERR;
        // 减少无卡片时的调试信息输出
        if (debug_count <= 3)
        {
            printf("MFRC522_Request: No card detected or communication error\r\n"); // 调试信息
        }
    }

    return status;
}

// 功    能：防冲突检测?读取选中卡片的卡序列号
// 参数说明: pSnr[OUT]:卡片序列号，4字节
// 返    回: 成功返回MI_OK
char MFRC522_Anticoll(unsigned char *pSnr)
{
    char status;
    unsigned char i, snr_check = 0;
    unsigned int unLen;
    unsigned char ucComMF522Buf[MAXRLEN];

    ClearBitMask(Status2Reg, 0x08);
    Write_MFRC522(BitFramingReg, 0x00);
    ClearBitMask(CollReg, 0x80);

    ucComMF522Buf[0] = PICC_ANTICOLL1;
    ucComMF522Buf[1] = 0x20;

    status = MFRC522_ToCard(PCD_TRANSCEIVE, ucComMF522Buf, 2, ucComMF522Buf, &unLen);

    if (status == MI_OK)
    {
        for (i = 0; i < 4; i++)
        {
            *(pSnr + i) = ucComMF522Buf[i];
            snr_check ^= ucComMF522Buf[i];
        }
        if (snr_check != ucComMF522Buf[i])
        {
            status = MI_ERR;
        }
    }

    SetBitMask(CollReg, 0x80);
    return status;
}

/////////////////////////////////////////////////////////////////////
// 功    能：选定卡片
// 参数说明: pSnr[IN]:卡片序列号，4字节
// 返    回: 成功返回MI_OK
/////////////////////////////////////////////////////////////////////
char MFRC522_SelectTag(unsigned char *pSnr)
{
    char status;
    unsigned char i;
    unsigned int unLen;
    unsigned char ucComMF522Buf[MAXRLEN];

    ucComMF522Buf[0] = PICC_ANTICOLL1;
    ucComMF522Buf[1] = 0x70;
    ucComMF522Buf[6] = 0;
    for (i = 0; i < 4; i++)
    {
        ucComMF522Buf[i + 2] = *(pSnr + i);
        ucComMF522Buf[6] ^= *(pSnr + i);
    }
    CalulateCRC(ucComMF522Buf, 7, &ucComMF522Buf[7]);

    ClearBitMask(Status2Reg, 0x08);

    status = MFRC522_ToCard(PCD_TRANSCEIVE, ucComMF522Buf, 9, ucComMF522Buf, &unLen);

    if ((status == MI_OK) && (unLen == 0x18))
    {
        status = MI_OK;
    }
    else
    {
        status = MI_ERR;
    }

    return status;
}

/////////////////////////////////////////////////////////////////////
// 功    能：验证卡片密码
// 参数说明: auth_mode[IN]: 密码验证模式
//                  0x60 = 验证A密钥
//                  0x61 = 验证B密钥
//           addr[IN]：块地址
//           pKey[IN]：密码
//           pSnr[IN]：卡片序列号，4字节
// 返    回: 成功返回MI_OK
/////////////////////////////////////////////////////////////////////
char MFRC522_AuthState(unsigned char auth_mode, unsigned char addr, unsigned char *pKey, unsigned char *pSnr)
{
    char status;
    unsigned int unLen;
    unsigned char i, ucComMF522Buf[MAXRLEN];

    ucComMF522Buf[0] = auth_mode;
    ucComMF522Buf[1] = addr;
    for (i = 0; i < 6; i++)
    {
        ucComMF522Buf[i + 2] = *(pKey + i);
    }
    for (i = 0; i < 6; i++)
    {
        ucComMF522Buf[i + 8] = *(pSnr + i);
    }
    //   memcpy(&ucComMF522Buf[2], pKey, 6);
    //   memcpy(&ucComMF522Buf[8], pSnr, 4);

    status = MFRC522_ToCard(PCD_AUTHENT, ucComMF522Buf, 12, ucComMF522Buf, &unLen);
    if ((status != MI_OK) || (!(Read_MFRC522(Status2Reg) & 0x08)))
    {
        status = MI_ERR;
    }

    return status;
}

/////////////////////////////////////////////////////////////////////
// 功    能：读取M1卡一块数据
// 参数说明: addr[IN]：块地址
//           pData[OUT]：读出的数据，16字节
// 返    回: 成功返回MI_OK
/////////////////////////////////////////////////////////////////////
char MFRC522_Read(unsigned char addr, unsigned char *pData)
{
    char status;
    unsigned int unLen;
    unsigned char i, ucComMF522Buf[MAXRLEN];

    ucComMF522Buf[0] = PICC_READ;
    ucComMF522Buf[1] = addr;
    CalulateCRC(ucComMF522Buf, 2, &ucComMF522Buf[2]);

    status = MFRC522_ToCard(PCD_TRANSCEIVE, ucComMF522Buf, 4, ucComMF522Buf, &unLen);
    if ((status == MI_OK) && (unLen == 0x90))
    //   {   memcpy(pData, ucComMF522Buf, 16);   }
    {
        for (i = 0; i < 16; i++)
        {
            *(pData + i) = ucComMF522Buf[i];
        }
    }
    else
    {
        status = MI_ERR;
    }

    return status;
}

/////////////////////////////////////////////////////////////////////
// 功    能：写数据到M1卡一块
// 参数说明: addr[IN]：块地址
//           pData[IN]：写入的数据，16字节
// 返    回: 成功返回MI_OK
/////////////////////////////////////////////////////////////////////
char MFRC522_Write(unsigned char addr, unsigned char *pData)
{
    char status;
    unsigned int unLen;
    unsigned char i, ucComMF522Buf[MAXRLEN];

    ucComMF522Buf[0] = PICC_WRITE;
    ucComMF522Buf[1] = addr;
    CalulateCRC(ucComMF522Buf, 2, &ucComMF522Buf[2]);

    status = MFRC522_ToCard(PCD_TRANSCEIVE, ucComMF522Buf, 4, ucComMF522Buf, &unLen);

    if ((status != MI_OK) || (unLen != 4) || ((ucComMF522Buf[0] & 0x0F) != 0x0A))
    {
        status = MI_ERR;
    }

    if (status == MI_OK)
    {
        // memcpy(ucComMF522Buf, pData, 16);
        for (i = 0; i < 16; i++)
        {
            ucComMF522Buf[i] = *(pData + i);
        }
        CalulateCRC(ucComMF522Buf, 16, &ucComMF522Buf[16]);

        status = MFRC522_ToCard(PCD_TRANSCEIVE, ucComMF522Buf, 18, ucComMF522Buf, &unLen);
        if ((status != MI_OK) || (unLen != 4) || ((ucComMF522Buf[0] & 0x0F) != 0x0A))
        {
            status = MI_ERR;
        }
    }

    return status;
}
