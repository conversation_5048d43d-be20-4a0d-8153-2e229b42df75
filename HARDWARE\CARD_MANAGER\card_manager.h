#ifndef __CARD_MANAGER_H
#define __CARD_MANAGER_H

#include "stm32f10x.h" // STM32F10x系列标准外设库头文件
#include <stdio.h>     // 标准输入输出函数
#include <string.h>    // 字符串操作函数

/*****************辰哥单片机设计******************
                                            STM32
 * 文件			:	卡片管理模块头文件
 * 版本			: V1.0
 * 日期			: 2024.9.2
 * MCU			:	STM32F103C8T6
 * 接口			:	RFID卡片管理
 * BILIBILI	:	辰哥单片机设计
 * CSDN			:	辰哥单片机设计
 * 作者			:	辰哥

**********************BEGIN***********************/

/////////////////////////////////////////////////////////////////////
// 卡片管理相关数据类型定义 - 应用层数据结构
/////////////////////////////////////////////////////////////////////

// 卡片数据库大小定义
#define card_size 64

// 卡片信息结构体定义
typedef struct
{
    uint8_t id[20];       // 卡片ID（支持长ID格式）
    uint8_t type[2];      // 卡片类型（2字节）
    uint8_t used_flag;    // 使用标志（0=未使用，1=已使用）
    char name[16];        // 卡片名称（用户友好标识）
    uint8_t access_level; // 访问权限级别（0-9，9为最高权限）
} ic_card_t;

// 卡片验证结果枚举
typedef enum
{
    CARD_VERIFY_SUCCESS = 0, // 验证成功
    CARD_VERIFY_FAILED,      // 验证失败（未授权卡片）
    CARD_READ_ERROR,         // 读取错误（硬件通信失败）
    CARD_NOT_FOUND           // 未检测到卡片
} CardVerifyResult_t;

// 卡片管理标志位定义
#define MFRC522_FLAG_ADD 0x01 // 添加新卡片标志位
extern uint8_t MFRC522_flag;  // 卡片管理标志变量（外部声明）

/////////////////////////////////////////////////////////////////////
// 函数声明区域 - 卡片管理功能接口
/////////////////////////////////////////////////////////////////////

// 卡片数据库管理函数
int8_t CARD_find_card(ic_card_t *card); // 查找卡片：在数据库中查找指定卡片
int8_t CARD_add_card(ic_card_t *card);  // 添加卡片：向数据库添加新卡片
void CARD_InitDatabase(void);           // 数据库初始化：预设授权卡片数据
void CARD_Proc(void);                   // 卡片处理：完整的卡片读取、验证和输出流程

// 工具函数
uint32_t CARD_uchar_to_uint(uint8_t *buffer, uint8_t size); // 字节数组转换为无符号整数

#endif /* __CARD_MANAGER_H */
